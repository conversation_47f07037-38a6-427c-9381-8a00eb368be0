import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { DashboardPage } from './pages/warehouse/warehouse.page';
import { OdoorpcService } from './shared/services/odoorpc.service';
import { NewPurchaseOrderPage } from './pages/purchase-order/new/new-purchase-order.page';
import { LoginPage } from './pages/login/login.page';
import { SalesBrowserPage } from './pages/sales/sales.page';
import { PickingPage } from './pages/picking-page/picking-page';
import { ProductionPage } from './pages/production/production.page';
import { ProductionBrowserPage } from './pages/production-browser/production-browser.page';
import { PickingBrowserPage } from './pages/picking-browser/picking-browser.component';
import { WelcomePage } from './pages/welcome/welcome.component';
import { DailyReplenishmentPageComponent } from './pages/daily-production-page/daily-production-page.component';
import { SearchComponent } from './pages/search/search.component';
import { LiberalizedComponent } from './components/liberalized/liberalized.component';
import { ScanBluPageComponent } from './components/scanblu-page/scanblu-page.component'
import { ScaetoDetail } from './pages/scarto-detail/scarto-detail.page'
import { PesaturaBancalePage } from './pages/pesatura-bancale/pesatura-bancale.page';
import { TanaNuovoBancalePage } from './pages/tara-nuovo-bancale/tara-nuovo-bancale.page';
import { EtichettaSaccoBluPage } from './pages/etichetta-sacco-blu/etichetta-sacco-blu.page';
import { TaraBancalePage } from './pages/tara-bancale/tara-bancale.page';
import { BlocchetiProdottiComponent } from './pages/bloccheti-prodotti/bloccheti-prodotti.component'
import { PelaturaPage } from './pages/pelatura/pelatura.page';
import { ProductionZolaMascaPage } from './pages/production-zolamasca/production-zolamasca.page';
import { PelaturaProductionComponent } from './pelatura-production/pelatura-production.component';

const routes: Routes = [
  {
    path: '',
    component: WelcomePage,
    canActivate: [OdoorpcService]
  },
  {
    path: 'warehouse',
    component: DashboardPage,
    canActivate: [OdoorpcService]
  },
  {
    path: 'pelatura',
    component: PelaturaPage,
    canActivate: [OdoorpcService]
  },
  {
    path: 'pelatura/:id',
    component: PelaturaProductionComponent,
    canActivate: [OdoorpcService]
  },

   {
    path: 'pelatura/picking/:id',
    component: PickingPage,
    canActivate: [OdoorpcService]
  },
  {
    path: 'production-zolamasca/:id',
    component: ProductionZolaMascaPage,
    canActivate: [OdoorpcService]
  },
  {
    path: 'login',
    component: LoginPage
  },
  {
    path: 'production',
    component: ProductionBrowserPage,
    canActivate: [OdoorpcService]
  },
  {
    path: 'scarto-detail',
    component: ScaetoDetail,
    canActivate: [OdoorpcService]
  },
  {
    path: 'scan-blu',
    component: ScanBluPageComponent,
    canActivate: [OdoorpcService]
  },
  {
    path: 'liberalized',
    component: LiberalizedComponent,
    canActivate: [OdoorpcService]
  },
  {
    path: 'search/:id',
    component: SearchComponent,
    canActivate: [OdoorpcService]
  },
  {
    path: 'production/:id',
    component: ProductionPage,
    canActivate: [OdoorpcService]
  },
  {
    path: 'warehouse/daily',
    component: DailyReplenishmentPageComponent,
    canActivate: [OdoorpcService]
  },
  {
    path: 'warehouse/daily/new/:replenishment_id',
    component: NewPurchaseOrderPage,
    canActivate: [OdoorpcService]
  },
  {
    path: 'warehouse/purchase_order/new',
    component: NewPurchaseOrderPage,
    canActivate: [OdoorpcService]
  },
  {
    path: 'warehouse/pickings/:id',
    component: PickingBrowserPage,
    canActivate: [OdoorpcService]
  },
  {
    path: 'warehouse/picking/:id',
    component: PickingPage,
    canActivate: [OdoorpcService]
  },
  {
    path: 'pesatura-bancale',
    component: PesaturaBancalePage,
    canActivate: [OdoorpcService]
  },
  {
    path: 'tara-nuovo-bancale',
    component: TanaNuovoBancalePage,
    canActivate: [OdoorpcService]
  },
  {
    path: 'etichetta-sacco-blu',
    component: EtichettaSaccoBluPage,
    canActivate: [OdoorpcService]
  },
  {
    path: 'tara-bancale',
    component: TaraBancalePage,
    canActivate: [OdoorpcService]
  },
  {
    path: 'bloccheti-prodotti',
    component: BlocchetiProdottiComponent,
    canActivate: [OdoorpcService]
  }
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule {


}
