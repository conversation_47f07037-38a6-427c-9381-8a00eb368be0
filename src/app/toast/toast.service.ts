import { Injectable } from '@angular/core';
import { MessageService } from 'primeng/api';

@Injectable({
  providedIn: 'root'
})
export class ToastService {

  constructor(private messageService: MessageService) {
    // Toast Config Here
  }

  success(message: string = 'Operation completed successfully!', title: string = 'Success') {
    this.messageService.add({ severity: 'success', detail: message, summary: title });
  }

  info(message: string = '', title: string = 'Info') {
    this.messageService.add({ severity: 'info', detail: message, summary: title });
  }

  warn(message: string = '', title: string = 'Warning') {
    this.messageService.add({ severity: 'warn', detail: message, summary: title });
  }

  error(message: string = 'Something went wrong!', title: string = 'Error') {
    this.messageService.add({ severity: 'error', detail: message, summary: title });
  }
}
