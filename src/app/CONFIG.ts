export class CONFIG {

    static purchase_partner_id = 7

    // usata da stagionatura
    static reception_route_id = 67

    static picking_type_caseificio = 101
    static picking_type_tara = 105
    static picking_type_zola_masca = 265
    static picking_type_pelatura = 172

    // map picking type id -> weight field
    // static picking_type_produzione2 = 180 
    static sequence_production2 = "BA/PR2/"

    static liberalizzati = 999 // TODO GIULIO
    static confezionamento_location = 265
    static confezionamento_type_id = 199

    static typemap = {
        105: 'tara',
        107: 'weight_before_room',
        11: 'weight_after_room',
    }
    
    static checkmap = {
        11: {
            "Salatura": 'salted',
            "Etichettatura": 'stamped'
        }
    }

    static seasoning_location_id = 25
    
    static picking_types_with_destination = [107]
    static picking_types_filtered = [] // 101 produzione forme  , 103 inscatolamento
    static head_picking_type = 103
    static cameracalda_location_id = 135;
}