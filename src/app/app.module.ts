import { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';
import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ProductPickerComponent } from './components/product-picker/product-picker.component';
import { SignatureComponent } from './components/signature/signature.component';
import { OrderCardComponent } from './components/order-card/order-card.component';
import { OrderPickerComponent } from './components/order-picker/order-picker.component';
import { AreaWizardComponent } from './components/area-wizard/area-wizard.component';
import { TaskBrowserComponent } from './components/task-browser/task-browser.component';
import { NavComponent } from './components/nav/nav.component';
import { WorkOrderStateComponent } from './components/work-order-state/work-order-state.component';
import { LoginPage } from './pages/login/login.page';
import { DashboardPage } from './pages/warehouse/warehouse.page';

// import { NewMrpProductionFormComponent } from './components/mrp-production-new-form/mrp-production-new-form.component';
// import { NewMrpProductionPage } from './pages/mrp/new/new-mrp-production.page';
import { TaskPickerComponent } from './components/task-picker/task-picker.component'; // stagionatua
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { BreadcrumbComponent } from './components/breadcrumb/breadcrumb.component';
import { CalibrationPalletModalComponent } from './components/modals/calibration/calibration.modal';
import { AgingModalComponent } from './components/modals/aging/aging.modal';
import { AuthInterceptor } from './shared/services/auth-interceptor.service';
import { CommonModule } from '@angular/common';


import { SalesBrowserPage } from './pages/sales/sales.page'; // confezionamento
import { MrpProductionModal } from './components/modals/mrp-production/mrp-production.modal';
import { InputModal } from './components/modals/input/input.modal';
import { PickingPage } from './pages/picking-page/picking-page';
import { NewPurchaseOrderPage } from './pages/purchase-order/new/new-purchase-order.page';
import { ProductionPage } from './pages/production/production.page';
import { ProductionBrowserPage } from './pages/production-browser/production-browser.page';
import { PickingBrowserPage } from './pages/picking-browser/picking-browser.component';
import { StockQuantModal } from './components/modals/stock-quant/stock-quant.modal';
import { WelcomePage } from './pages/welcome/welcome.component';
import { ScanPackageModal } from './components/modals/scan-package/scan-package.modal';
import { ScanPackageCodeModal } from './components/modals/scan-package-code/scan-package-code.modal';
import { WeighInputComponent } from './components/weigh-input/weigh-input.component';
import { ServiceWorkerModule } from '@angular/service-worker';
import { environment } from '../environments/environment';
import { DailyReplenishmentPageComponent } from './pages/daily-production-page/daily-production-page.component';
import { SearchComponent } from './pages/search/search.component';
import { LiberalizedComponent } from './components/liberalized/liberalized.component';
import { DatePipe } from '@angular/common';

import { ScanBluPageComponent } from './components/scanblu-page/scanblu-page.component'
import { ScaetoDetail } from './pages/scarto-detail/scarto-detail.page';
import { ScartoDetailModalComponent } from './components/scarto-detail-modal/scarto-detail-modal.component'
import { ScartoConfirmModalComponent } from './components/scarto-confirm-modal/scarto-confirm-modal.component'
import { PesaturaBancalePage } from './pages/pesatura-bancale/pesatura-bancale.page';
import { CalculatorModalComponent } from './components/calculator-modal/calculator-modal.component';
import { SortByCreateDatePipe, TanaNuovoBancalePage } from './pages/tara-nuovo-bancale/tara-nuovo-bancale.page';
import { EtichettaSaccoBluPage } from './pages/etichetta-sacco-blu/etichetta-sacco-blu.page';
import { TaraBancalePage } from './pages/tara-bancale/tara-bancale.page';
import { BlocchetiProdottiComponent } from './pages/bloccheti-prodotti/bloccheti-prodotti.component';


// primeng

import { ToastModule } from './toast/toast.module';
import { DialogModule } from 'primeng/dialog';
import { PelaturaPage } from './pages/pelatura/pelatura.page';
import { ProductionZolaMascaPage } from './pages/production-zolamasca/production-zolamasca.page';
import { PelaturaProductionComponent } from './pelatura-production/pelatura-production.component';

@NgModule({
  declarations: [
    AppComponent,
    LoginPage,
    DashboardPage,
    PelaturaPage,
    ProductPickerComponent,
    SignatureComponent,
    OrderCardComponent,
    OrderPickerComponent,
    AreaWizardComponent,
    TaskBrowserComponent,
    NavComponent,
    WorkOrderStateComponent,
    NewPurchaseOrderPage,
    CalibrationPalletModalComponent,
    SalesBrowserPage,
    TaskPickerComponent,
    BreadcrumbComponent,
    AgingModalComponent,
    MrpProductionModal,
    InputModal,
    PickingPage,
    ProductionPage,
    ProductionBrowserPage,
    PickingBrowserPage,
    StockQuantModal,
    WelcomePage,
    ScanPackageModal,
    ScanPackageCodeModal,
    WeighInputComponent,
    DailyReplenishmentPageComponent,
    SearchComponent,
    LiberalizedComponent,
    ScartoDetailModalComponent,
    ScaetoDetail,
    ScanBluPageComponent,
    ScartoConfirmModalComponent,
    PesaturaBancalePage,
    CalculatorModalComponent,
    TanaNuovoBancalePage,
    EtichettaSaccoBluPage,
    TaraBancalePage,
    BlocchetiProdottiComponent,
    SortByCreateDatePipe,
    ProductionZolaMascaPage,
    PelaturaProductionComponent
  ],
  exports: [NavComponent],
  imports: [
    // primeng imports
    ToastModule,
    DialogModule,
    // end primeng imports
    BrowserModule,
    BrowserAnimationsModule,
    AppRoutingModule,
    FormsModule,
    NgbModule,
    ReactiveFormsModule,
    HttpClientModule,
    CommonModule,
    ServiceWorkerModule.register('ngsw-worker.js', {
      enabled: environment.production,
      // Register the ServiceWorker as soon as the application is stable
      // or after 30 seconds (whichever comes first).
      registrationStrategy: 'registerWhenStable:30000'
    }),
    
  ],
  providers: [{
    
    provide: HTTP_INTERCEPTORS,
    useClass: AuthInterceptor,
    multi: true
  }, DatePipe
  ],
  bootstrap: [AppComponent]
})
export class AppModule { }