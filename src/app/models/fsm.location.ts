import { OdooModel } from "../shared/interfaces/odoo-model.model";
import { OdooRelationship } from "../shared/interfaces/odoo-relationship.model";
import { OdooSerializableInterface } from "../shared/interfaces/odoo-serializable-interface";
import { Partner } from "./partner";

export class FSMLocation extends OdooModel implements OdooSerializableInterface<FSMLocation> {
    ODOO_FIELDS?: string[] | undefined;


    public readonly ODOO_MODEL = 'fsm.location';
    name:string = ""
    street:string = ""
    city:string = ""
    owner_id:OdooRelationship<Partner> = new OdooRelationship<Partner>()
    fsm_parent_id:OdooRelationship<FSMLocation> = new OdooRelationship<FSMLocation>()
    


    create() {
        return new FSMLocation()
    }

}