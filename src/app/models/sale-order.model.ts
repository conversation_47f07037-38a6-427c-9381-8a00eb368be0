import { OdooModel } from "../shared/interfaces/odoo-model.model";
import { OdooMultiRelationship } from "../shared/interfaces/odoo-multi-relationship.model";
import { OdooRelationship } from "../shared/interfaces/odoo-relationship.model";
import { OdooSerializableInterface } from "../shared/interfaces/odoo-serializable-interface";
import { Partner } from "./partner";
import { SaleOrderLine } from "./sale-order-line.model";

export class SaleOrder extends OdooModel implements OdooSerializableInterface<SaleOrder> {
  public readonly ODOO_MODEL = 'sale.order';

  order_line: OdooMultiRelationship<SaleOrderLine>
  partner_id:OdooRelationship<Partner> = new OdooRelationship<Partner>()
  name : string  = ""
  date_order : string = ""
  state : string = ""
  validity_date : string = ""

  invoice_status : string = ""
  amount_total : string =""

  // lot_name:string = ""

  origin = ""
  // bs_shipping_partner = ""
  // bs_sale_order_number = ""

  
  constructor(id?: number) {
    super(id);
    // this.incoterm = new OdooRelationship();
    this.order_line = new OdooMultiRelationship<SaleOrderLine>(SaleOrderLine);
  }
  create() {
    return new SaleOrder()
  }

  ODOO_FIELDS?: string[];


}
