import { OdooModel } from "../shared/interfaces/odoo-model.model";
import { OdooMultiRelationship } from "../shared/interfaces/odoo-multi-relationship.model";
import { OdooSerializableInterface } from "../shared/interfaces/odoo-serializable-interface";
import { Pallet } from "./pallet.model";
import { MrpWorkorder } from "./mrp.workorder";
import { OdooRelationship } from "../shared/interfaces/odoo-relationship.model";
import { ProductProduct } from "./product.product";
import { StockMove } from "./stock.move";
import { StockProductionLot } from "./stock.production-lot";
import { MrpProduction } from "./mrp.production.model";
import { MrpProductionBackorder } from "./mrp.production.backorder.model";

export class MrpProductionBackorderLine extends OdooModel implements OdooSerializableInterface<MrpProductionBackorderLine> {
    public readonly ODOO_MODEL = 'mrp.production.backorder.line'; 
    mrp_production_id : OdooRelationship<MrpProduction> = new OdooRelationship<MrpProduction>();
    mrp_production_backorder_id : OdooRelationship<MrpProductionBackorder> = new OdooRelationship<MrpProductionBackorder>();

    name:string = "";
    to_backorder: boolean = true
    constructor(id?: number ){ 
        super(id);
     }

    create(): MrpProductionBackorderLine {
        return new MrpProductionBackorderLine()
    }

}