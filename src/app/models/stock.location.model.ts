import { OdooModel } from "../shared/interfaces/odoo-model.model";
import { OdooRelationship } from "../shared/interfaces/odoo-relationship.model";
import { OdooSerializableInterface } from "../shared/interfaces/odoo-serializable-interface";

export class StockLocation extends OdooModel implements OdooSerializableInterface<StockLocation> {
    public readonly ODOO_MODEL = 'stock.location';
    name:string = ""

    location_id:OdooRelationship<StockLocation> = new OdooRelationship<StockLocation>()

    create() {
        return new StockLocation()
    }

}