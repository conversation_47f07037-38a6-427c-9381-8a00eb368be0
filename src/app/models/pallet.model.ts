import { OdooModel } from "../shared/interfaces/odoo-model.model";
import { OdooMultiRelationship } from "../shared/interfaces/odoo-multi-relationship.model";
import { OdooRelationship } from "../shared/interfaces/odoo-relationship.model";
import { OdooSerializableInterface } from "../shared/interfaces/odoo-serializable-interface";

export class Pallet extends OdooModel implements OdooSerializableInterface<Pallet> {
    public readonly ODOO_MODEL = 'pallet'; 
    num: number = 0
    quantity: number = 0

    start_ageing: string = ""
    end_ageing: string = ""
    start_room: string = ""
    end_room : string = ""
    tara_date: string = ""
    
    room_code : string = ""
    cell_code : string = ""

    tara: number = 0
    weight_after_room: number = 0
    weight_before_room: number = 0

    create(): Pallet {
        return new Pallet()
    }

}




// class Pallet(models.Model):
//     _name = "pallet"

//     code = fields.Char("code")

//     production_batch_id = fields.Many2one("mrp.production", "production_batch_id")

//     tara = fields.Float("tara")
//     weight_before_room = fields.Float("weight_before_room")
//     start_room = fields.Date("start_room")
//     end_room = fields.Date("end_room")
//     weight_after_room = fields.Float("weight_after_room")
//     start_ageing = fields.Date("start_ageing")
//     end_ageing = fields.Date("end_ageing")