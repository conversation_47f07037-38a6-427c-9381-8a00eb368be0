import { OdooModel } from "../shared/interfaces/odoo-model.model";
import { OdooRelationship } from "../shared/interfaces/odoo-relationship.model";
import { OdooSerializableInterface } from "../shared/interfaces/odoo-serializable-interface";
import { ProductTemplate } from "./product.template";

export class MrpBom extends OdooModel implements OdooSerializableInterface<MrpBom> {
  public readonly ODOO_MODEL = 'mrp.bom';


  // default_code: number;
  // codice_fornitore: number;
  // description_sale: string;
  // description_purchase: string = "";
  // prezzo_impresa: string;
  // qty_available: number;
  // qty_on_sale: number;
  // list_price: number;
  // inventory_qty_needed: number;
  // fornitoreName: string = "";
  // fornitore:OdooRelationship = new OdooRelationship(); 
  // product_packaging:OdooRelationship = new OdooRelationship(); 
  // uom_po_id:OdooRelationship = new OdooRelationship(); 
  // uom_id:OdooRelationship = new OdooRelationship();
  // categ_id:OdooRelationship = new OdooRelationship();
  product_tmpl_id:OdooRelationship<ProductTemplate> = new OdooRelationship<ProductTemplate>()

  constructor(id?: number) {
    super(id);
    
    
    // this.default_code = default_code;
    // this.codice_fornitore = codice_fornitore;
    // this.description_sale = description_sale;
    // this.prezzo_impresa = prezzo_impresa;
    // this.qty_available = qty_available;
    // this.list_price = list_price;
    // this.uom_id = uom_id;
    // this.qty_on_sale = 0;
    // this.inventory_qty_needed = inventory_qty_needed ;
  }

  create(): MrpBom {
    return new MrpBom();
  }
}
