import { OdooModel } from "../shared/interfaces/odoo-model.model";
import { OdooRelationship } from "../shared/interfaces/odoo-relationship.model";
import { OdooSerializableInterface } from "../shared/interfaces/odoo-serializable-interface";

export class FSMEquipment extends OdooModel implements OdooSerializableInterface<FSMEquipment> {

    public readonly ODOO_MODEL = 'fsm.equipment';
    name:string = ""
    current_location_id:OdooRelationship<FSMEquipment> = new OdooRelationship<FSMEquipment>()

    create() {
        return new FSMEquipment()
    }

}