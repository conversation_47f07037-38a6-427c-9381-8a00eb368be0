import { OdooModel } from "../shared/interfaces/odoo-model.model";
import { OdooRelationship } from "../shared/interfaces/odoo-relationship.model";
import { OdooSerializableInterface } from "../shared/interfaces/odoo-serializable-interface";

export class UomUom extends OdooModel implements OdooSerializableInterface<UomUom> {
    public readonly ODOO_MODEL = 'uom.uom';
    name:string = ""

    create() {
        return new UomUom()
    }

}