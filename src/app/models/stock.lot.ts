import { DailyReplenishmentPageComponent } from "../pages/daily-production-page/daily-production-page.component";
import { OdooModel } from "../shared/interfaces/odoo-model.model";
import { OdooRelationship } from "../shared/interfaces/odoo-relationship.model";
import { OdooSerializableInterface } from "../shared/interfaces/odoo-serializable-interface";
import { BassiDailyReplenishment as BassiDailyReplenishment } from "./bassi.daily_production_row";
import { ProductProduct } from "./product.product";

export class StockLot extends OdooModel implements OdooSerializableInterface<StockLot> {
  public readonly ODOO_MODEL = 'stock.lot';

  name:string = ""
  product_id:OdooRelationship<ProductProduct> = new OdooRelationship<ProductProduct>();

  create() {
    return new StockLot();
  }
}