import { OdooModel } from "../shared/interfaces/odoo-model.model";
import { OdooMultiRelationship } from "../shared/interfaces/odoo-multi-relationship.model";
import { OdooRelationship } from "../shared/interfaces/odoo-relationship.model";
import { OdooSerializableInterface } from "../shared/interfaces/odoo-serializable-interface";
import { ProductTemplate } from "./product.template";
import { ProductTemplateAttributeValue } from "./product.template.attribute.value.model";

export class ProductProduct extends OdooModel implements OdooSerializableInterface<ProductProduct> {
    ODOO_FIELDS?: string[] | undefined;


    public readonly ODOO_MODEL = 'product.product';
    name:string = "";
    display_name: string = "";
    qty_available: number = 0
    qty_on_sale: number;
    uom_po_id:OdooRelationship = new OdooRelationship(); 
    uom_id:OdooRelationship = new OdooRelationship();
    qty:number;
    product_tmpl_id:OdooRelationship<ProductTemplate> = new OdooRelationship<ProductTemplate>()
    product_template_attribute_value_ids = new OdooMultiRelationship<ProductTemplateAttributeValue>(ProductTemplateAttributeValue);
    sale_ok: boolean = false;


    create() {
        return new ProductProduct()
    }

}