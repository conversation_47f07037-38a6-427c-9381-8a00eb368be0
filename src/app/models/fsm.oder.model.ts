import { OdooModel } from "../shared/interfaces/odoo-model.model";
import { OdooMultiRelationship } from "../shared/interfaces/odoo-multi-relationship.model";
import { OdooRelationship } from "../shared/interfaces/odoo-relationship.model";
import { OdooSerializableInterface } from "../shared/interfaces/odoo-serializable-interface";
import { FSMLocation } from "./fsm.location";
import { FSMOrderExecution } from "./fsm.order.execution.model";
import { Stage } from "./stage";

export class FSMOrder extends OdooModel implements OdooSerializableInterface<FSMOrder> {
    ODOO_FIELDS?: string[] | undefined;
    static STAGE_ID_OPEN = 4
    static STAGE_ID_CLOSED = 5
    public readonly ODOO_MODEL = 'fsm.order';
    name:string = ""
    stage_id:OdooRelationship<Stage> = new OdooRelationship<Stage>()
    location_id:OdooRelationship<FSMLocation> = new OdooRelationship<FSMLocation>()
    execution_ids:OdooMultiRelationship<FSMOrderExecution> = new OdooMultiRelationship<FSMOrderExecution>(FSMOrderExecution)

    create() {
        return new FSMOrder()
    }

}