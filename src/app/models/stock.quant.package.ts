import { OdooModel } from "../shared/interfaces/odoo-model.model";
import { OdooMultiRelationship } from "../shared/interfaces/odoo-multi-relationship.model";
import { OdooRelationship } from "../shared/interfaces/odoo-relationship.model";
import { OdooSerializableInterface } from "../shared/interfaces/odoo-serializable-interface";
import { PackageWeightTracking } from "./package.weight.tracking";
import { StockLocation } from "./stock.location.model";
import { StockProductionLot } from "./stock.production-lot";
import { StockQuant } from "./stock.quant.model";



export class StockQuantPackage extends OdooModel implements OdooSerializableInterface<StockQuantPackage> {
   
    public readonly ODOO_MODEL = 'stock.quant.package';
    name:string = ""
    location_id:OdooRelationship<StockLocation> = new OdooRelationship<StockLocation>();
    quant_ids:  OdooMultiRelationship<StockQuant> = new OdooMultiRelationship<StockQuant>(StockQuant); 
    poli_1:number = 0
    poli_2:number = 0
    // custom
    weight_tracking_ids:OdooMultiRelationship<PackageWeightTracking> = new OdooMultiRelationship<PackageWeightTracking>(PackageWeightTracking);
    create_uid:OdooRelationship<any> = new OdooRelationship<any>()
    create_date:string = ""
    package_type_id:number = 0; // 0 = sacco, 1 = cartone, 2 = pallet, 3 = altro
    // tara:number = 0;
    // tara_date:Date = new Date();
    // weight_before_room:number = 0;
    // start_room:Date = new Date();
    // end_room:Date = new Date();
    // weight_after_room:number = 0;
    // start_ageing:Date = new Date();
    // end_ageing:Date = new Date()



    
    create() {
        return new StockQuantPackage()
    }


    
}