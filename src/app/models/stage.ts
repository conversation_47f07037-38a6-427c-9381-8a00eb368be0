import { OdooModel } from "../shared/interfaces/odoo-model.model";
import { OdooRelationship } from "../shared/interfaces/odoo-relationship.model";
import { OdooSerializableInterface } from "../shared/interfaces/odoo-serializable-interface";

export class Stage extends OdooModel implements OdooSerializableInterface<Stage> {
    public readonly ODOO_MODEL = 'crm.case.stage';

    name: string;
    sequence: number;
  
    constructor(id?: number, name?: string, sequence?:number) {
        super(id);
        if (name) this.name = name
        if (sequence) this.sequence = sequence
    }
  
    create(): Stage {
      return new Stage();
    }
  }
  