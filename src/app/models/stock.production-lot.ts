import { DailyReplenishmentPageComponent } from "../pages/daily-production-page/daily-production-page.component";
import { OdooModel } from "../shared/interfaces/odoo-model.model";
import { OdooRelationship } from "../shared/interfaces/odoo-relationship.model";
import { OdooSerializableInterface } from "../shared/interfaces/odoo-serializable-interface";
import { BassiDailyReplenishment as BassiDailyReplenishment } from "./bassi.daily_production_row";
import { ProductProduct } from "./product.product";

export class StockProductionLot extends OdooModel implements OdooSerializableInterface<StockProductionLot> {
  public readonly ODOO_MODEL = 'stock.production.lot';

  name:string = ""
  product_id:OdooRelationship<ProductProduct> = new OdooRelationship<ProductProduct>();
  state = ""
  production_date = ""
  aging_days = ""

  create() {
    return new StockProductionLot();
  }
}