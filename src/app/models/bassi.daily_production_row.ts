import { OdooModel } from "../shared/interfaces/odoo-model.model";
import { OdooMultiRelationship } from "../shared/interfaces/odoo-multi-relationship.model";
import { OdooRelationship } from "../shared/interfaces/odoo-relationship.model";
import { OdooSerializableInterface } from "../shared/interfaces/odoo-serializable-interface";
import { ProductProduct } from "./product.product";
import { StockProductionLot } from "./stock.production-lot";

export class BassiDailyReplenishment extends OdooModel implements OdooSerializableInterface<BassiDailyReplenishment> {
    public readonly ODOO_MODEL = 'bassi.daily_replenishment';
    
    date:Date = new Date()
    product_id:OdooRelationship<ProductProduct> = new OdooRelationship<ProductProduct>() 
    // lot_id:OdooRelationship<StockProductionLot> = new OdooRelationship<StockProductionLot>() 
    quantity:number = 0
    lot_ids:OdooMultiRelationship<StockProductionLot> = new OdooMultiRelationship<StockProductionLot>(new StockProductionLot())

    create() {
        return new BassiDailyReplenishment()
    }
}
