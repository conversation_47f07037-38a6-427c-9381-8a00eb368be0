import { OdooModel } from "../shared/interfaces/odoo-model.model";
import { OdooRelationship } from "../shared/interfaces/odoo-relationship.model";
import { OdooSerializableInterface } from "../shared/interfaces/odoo-serializable-interface";
import { MrpProduction } from "./mrp.production.model";
import { ProductProduct } from "./product.product";

export class PurchaseOrderLine extends OdooModel implements OdooSerializableInterface<PurchaseOrderLine> {

  public ODOO_MODEL = 'purchase.order.line';
  // qty_delivered_sale: number  = 0
  // confezioni_qty_lorda_delivered: number = 0
  // purchase_line_id: OdooRelationship;
  // fornitore_id: OdooRelationship;
  // purchase_id: OdooRelationship;
  // supplier_purchase_id: OdooRelationship;
  // notes: string = ""
  // working_code: string = ""
  // qty_lorda_so_pz = 0;

  production_batch_id:OdooRelationship<MrpProduction> = new OdooRelationship<MrpProduction>()
  available_qty : number = 0
  name : string  = ""
  order_id : number  = 0
  weight : number  = 0
  product_uom_qty : number = 0
  product_id:OdooRelationship<ProductProduct> = new OdooRelationship<ProductProduct>();
  price_subtotal : number = 0

  create(): PurchaseOrderLine {
    return new PurchaseOrderLine();
  }
}


