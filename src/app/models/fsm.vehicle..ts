import { OdooModel } from "../shared/interfaces/odoo-model.model";
import { OdooSerializableInterface } from "../shared/interfaces/odoo-serializable-interface";

export class FSMRefueling extends OdooModel implements OdooSerializableInterface<FSMRefueling> {
    ODOO_FIELDS?: string[] | undefined;


    public readonly ODOO_MODEL = 'fsm.refueling';
    name:string = ""

    create() {
        return new FSMRefueling()
    }

}