import { OdooModel } from "../shared/interfaces/odoo-model.model";
import { OdooMultiRelationship } from "../shared/interfaces/odoo-multi-relationship.model";
import { OdooRelationship } from "../shared/interfaces/odoo-relationship.model";
import { OdooSerializableInterface } from "../shared/interfaces/odoo-serializable-interface";
import { ProductProduct } from "./product.product";
import { StockLocation } from "./stock.location.model";
import { StockMoveLine } from "./stock.move.line";
import { StockPickingType } from "./stock.picking.type";

export class StockPicking extends OdooModel implements OdooSerializableInterface<StockPicking> {
    public readonly ODOO_MODEL = 'stock.picking';
    // name:string = ""
    name:string = "";

    state : string =""
    origin : string =""
    location_id:OdooRelationship<StockLocation> = new OdooRelationship<StockLocation>();
    location_dest_id:OdooRelationship<StockLocation> = new OdooRelationship<StockLocation>();
    picking_type_id:OdooRelationship<StockPickingType> = new OdooRelationship<StockPickingType>();
    move_line_ids_without_package:  OdooMultiRelationship<StockMoveLine> = new OdooMultiRelationship<StockMoveLine>(StockMoveLine); 
    scheduled_date : string =""
    date_done:string=""
    
    create() {
        return new StockPicking()
    }

}