import { OdooModel } from "../shared/interfaces/odoo-model.model";
import { OdooRelationship } from "../shared/interfaces/odoo-relationship.model";
import { OdooSerializableInterface } from "../shared/interfaces/odoo-serializable-interface";
import { MrpProduction } from "./mrp.production.model";
import { ProductProduct } from "./product.product";
import { StockLocation } from "./stock.location.model";
import { StockLot } from "./stock.lot";
import { StockMove } from "./stock.move";
import { StockPicking } from "./stock.picking";
import { StockProductionLot } from "./stock.production-lot";
import { StockQuantPackage } from "./stock.quant.package";
import { UomUom } from "./uom.uom.model";

export class StockMoveLine extends OdooModel implements OdooSerializableInterface<StockMoveLine> {
    public readonly ODOO_MODEL = 'stock.move.line';
    move_id: OdooRelationship<StockMove> = new OdooRelationship<StockMove>();
    lot_id:OdooRelationship<StockLot> = new OdooRelationship<StockLot>()
    location_id:OdooRelationship<StockLocation> = new OdooRelationship<StockLocation>();
    location_dest_id:OdooRelationship<StockLocation> = new OdooRelationship<StockLocation>();

    product_id:OdooRelationship<ProductProduct> = new OdooRelationship<ProductProduct>();
    result_package_id : OdooRelationship<StockQuantPackage> = new OdooRelationship<StockQuantPackage>();
    package_id: OdooRelationship<StockQuantPackage> = new OdooRelationship<StockQuantPackage>();
    
    // TODO GIULIO RENAME product_uom_qty -> reserved_uom_qty
    reserved_uom_qty : number = 0
    qty_done: number = 0
    state: string = "";
    picking_id:OdooRelationship<StockPicking> = new OdooRelationship<StockPicking>();
    production_id: OdooRelationship<MrpProduction> = new OdooRelationship<MrpProduction>();
    product_uom_id:OdooRelationship<UomUom> = new OdooRelationship<UomUom>();


    
    create() {
        return new StockMoveLine()
    }

}