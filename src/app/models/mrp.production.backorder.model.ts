import { OdooModel } from "../shared/interfaces/odoo-model.model";
import { OdooMultiRelationship } from "../shared/interfaces/odoo-multi-relationship.model";
import { OdooSerializableInterface } from "../shared/interfaces/odoo-serializable-interface";
import { <PERSON>llet } from "./pallet.model";
import { MrpWorkorder } from "./mrp.workorder";
import { OdooRelationship } from "../shared/interfaces/odoo-relationship.model";
import { ProductProduct } from "./product.product";
import { StockMove } from "./stock.move";
import { StockProductionLot } from "./stock.production-lot";

export class MrpProductionBackorder extends OdooModel implements OdooSerializableInterface<MrpProductionBackorder> {
    public readonly ODOO_MODEL = 'mrp.production.backorder'; 
    constructor(id?: number ){ 
        super(id);
     }

    create(): MrpProductionBackorder {
        return new MrpProductionBackorder()
    }

}