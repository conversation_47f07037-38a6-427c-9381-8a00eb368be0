import { OdooModel } from "../shared/interfaces/odoo-model.model";
import { OdooSerializableInterface } from "../shared/interfaces/odoo-serializable-interface";

export class FSMOrderExecution extends OdooModel implements OdooSerializableInterface<FSMOrderExecution> {
    // ODOO_FIELDS?: string[] | undefined;


    public readonly ODOO_MODEL = 'fsm.order.execution';
    name:string = ""
    date:string = ""
    beginTime: string = ""
    endTime:string = ""
    kilometers:number = 0

    // order_id:OdooRelationship<FSMOrderExecution> = new OdooRelationship<FSMOrderExecution>()
    
    create() {
        return new FSMOrderExecution()
    }

}