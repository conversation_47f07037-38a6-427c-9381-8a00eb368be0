import { OdooModel } from "../shared/interfaces/odoo-model.model";
import { OdooRelationship } from "../shared/interfaces/odoo-relationship.model";
import { OdooSerializableInterface } from "../shared/interfaces/odoo-serializable-interface";
import { StockPicking } from "./stock.picking";
import { StockPickingType } from "./stock.picking.type";

export class StockRule extends OdooModel implements OdooSerializableInterface<StockRule> {
    public readonly ODOO_MODEL = 'stock.rule';
    name:string = ""
    // location_id : OdooRelationship<any> = new OdooRelationship<any>()
    picking_type_id : OdooRelationship<StockPickingType> = new OdooRelationship<StockPickingType>()

    create() {
        return new StockRule()
    }

}