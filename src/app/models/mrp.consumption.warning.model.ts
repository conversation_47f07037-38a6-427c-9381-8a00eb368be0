import { OdooModel } from "../shared/interfaces/odoo-model.model";
import { OdooMultiRelationship } from "../shared/interfaces/odoo-multi-relationship.model";
import { OdooSerializableInterface } from "../shared/interfaces/odoo-serializable-interface";
import { Pallet } from "./pallet.model";
import { MrpWorkorder } from "./mrp.workorder";
import { OdooRelationship } from "../shared/interfaces/odoo-relationship.model";
import { ProductProduct } from "./product.product";
import { StockMove } from "./stock.move";
import { StockProductionLot } from "./stock.production-lot";
import { MrpProduction } from "./mrp.production.model";

export class MrpConsumptionWarning extends OdooModel implements OdooSerializableInterface<MrpConsumptionWarning> {
    public readonly ODOO_MODEL = 'mrp.consumption.warning'; 
    constructor(id?: number ){ 
        super(id);
     }
    mrp_production_ids:  OdooMultiRelationship<MrpProduction> = new OdooMultiRelationship<MrpProduction>(MrpProduction); 


    create(): MrpConsumptionWarning {
        return new MrpConsumptionWarning()
    }

}