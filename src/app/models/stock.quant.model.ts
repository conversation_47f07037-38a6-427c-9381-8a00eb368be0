import { OdooModel } from "../shared/interfaces/odoo-model.model";
import { OdooRelationship } from "../shared/interfaces/odoo-relationship.model";
import { OdooSerializableInterface } from "../shared/interfaces/odoo-serializable-interface";
import { ProductProduct } from "./product.product";
import { StockLocation } from "./stock.location.model";
import { StockLot } from "./stock.lot";
import { StockProductionLot } from "./stock.production-lot";
import { StockQuantPackage } from "./stock.quant.package";
import { UomUom } from "./uom.uom.model";

export class StockQuant extends OdooModel implements OdooSerializableInterface<StockQuant> {
    public readonly ODOO_MODEL = 'stock.quant';
    // name:string = ""
        // product_uom_id

    display_name:string = "";
    available_quantity:number = 0;
    product_id:OdooRelationship<ProductProduct> = new OdooRelationship<ProductProduct>();
    product_uom_id:OdooRelationship<UomUom> = new OdooRelationship<UomUom>();
    location_id:OdooRelationship<StockLocation> = new OdooRelationship<StockLocation>();
    quantity:number = 0
    on_hand:boolean = false
    package_id:OdooRelationship<StockQuantPackage> = new OdooRelationship<StockQuantPackage>()
    lot_id:OdooRelationship<StockLot> = new OdooRelationship<StockLot>();

    create() {
        return new StockQuant()
    }

}