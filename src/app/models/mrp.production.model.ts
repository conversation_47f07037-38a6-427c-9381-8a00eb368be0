import { OdooModel } from "../shared/interfaces/odoo-model.model";
import { OdooMultiRelationship } from "../shared/interfaces/odoo-multi-relationship.model";
import { OdooSerializableInterface } from "../shared/interfaces/odoo-serializable-interface";
import { Pallet } from "./pallet.model";
import { MrpWorkorder } from "./mrp.workorder";
import { OdooRelationship } from "../shared/interfaces/odoo-relationship.model";
import { ProductProduct } from "./product.product";
import { StockMove } from "./stock.move";
import { StockProductionLot } from "./stock.production-lot";
import { UomUom } from "./uom.uom.model";
import { StockPicking } from "./stock.picking";
import { SaleOrderLine } from "./sale-order-line.model";
import { SaleOrder } from "./sale-order.model";
import { StockPickingType } from "./stock.picking.type";

export class MrpProduction extends OdooModel implements OdooSerializableInterface<MrpProduction> {
    public readonly ODOO_MODEL = 'mrp.production'; 
    name:string = "";
    display_name:string = "";
    product_qty : number = 0
    product_uom_id: OdooRelationship<UomUom> = new OdooRelationship<UomUom>()
    qty_producing : number = 0;
    picking_type_id: OdooRelationship<StockPickingType> = new OdooRelationship<StockPickingType>();
    // pallets:  OdooMultiRelationship<Pallet> = new OdooMultiRelationship<Pallet>(Pallet); 
    move_raw_ids: OdooMultiRelationship<StockMove> = new OdooMultiRelationship<StockMove>(StockMove);
    workorder_ids:  OdooMultiRelationship<MrpWorkorder> = new OdooMultiRelationship<MrpWorkorder>(MrpWorkorder); 
    date_planned_start : string = ""  // non sicuro sia il campo "Data di Creazione"
    product_id:OdooRelationship<ProductProduct> = new OdooRelationship<ProductProduct>();
    lot_producing_id: OdooRelationship<StockProductionLot> = new OdooRelationship<StockProductionLot>();
    move_finished_ids: OdooMultiRelationship<StockMove> = new OdooMultiRelationship<StockMove>(StockMove); 
    state : string = ""
    picking_ids:OdooMultiRelationship<StockPicking> = new OdooMultiRelationship<StockPicking>(StockPicking); 
    origin: string = ""
    move_byproduct_ids: OdooMultiRelationship<StockMove> = new OdooMultiRelationship<StockMove>(StockMove); 
    // related_sale_order:OdooRelationship<SaleOrder> = new OdooRelationship<SaleOrder>();
    // related_sale_order_line:OdooRelationship<SaleOrderLine> = new OdooRelationship<SaleOrderLine>();
    
    // origin:string = ""
    constructor(id?: number ){ 
        super(id);
     }

    create(): MrpProduction {
        return new MrpProduction()
    }

}