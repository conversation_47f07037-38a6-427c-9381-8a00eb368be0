import { OdooModel } from "../shared/interfaces/odoo-model.model";
import { OdooMultiRelationship } from "../shared/interfaces/odoo-multi-relationship.model";
import { OdooRelationship } from "../shared/interfaces/odoo-relationship.model";
import { OdooSerializableInterface } from "../shared/interfaces/odoo-serializable-interface";
import { MrpProduction } from "./mrp.production.model";
import { ProductProduct } from "./product.product";
import { StockMoveLine } from "./stock.move.line";
import { StockPicking } from "./stock.picking";
import { StockPickingType } from "./stock.picking.type";

export class StockMove extends OdooModel implements OdooSerializableInterface<StockMove> {
    public readonly ODOO_MODEL = 'stock.move';
    name:string = "";
    state : string = ""
    origin : string =""
    product_id:OdooRelationship<ProductProduct> = new OdooRelationship<ProductProduct>();
    location_id : OdooRelationship<any> = new OdooRelationship<any>()
    move_line_ids: OdooMultiRelationship<StockMoveLine> = new OdooMultiRelationship<StockMoveLine>(StockMoveLine);
    location_dest_id : OdooRelationship<any> = new OdooRelationship<any>()
    product_uom_qty:number = 0;
    picking_id:OdooRelationship<StockPicking> = new OdooRelationship<StockPicking>();
    picking_type_id :OdooRelationship<StockPickingType> = new OdooRelationship<StockPickingType>();
    production_id:OdooRelationship<MrpProduction> = new OdooRelationship<MrpProduction>();
    product_qty: number = 0
    quantity_done:number = 0;
    forecast_availability:number = 0

    create() {
        return new StockMove()
    }

}