import { OdooModel } from "../shared/interfaces/odoo-model.model";
import { OdooRelationship } from "../shared/interfaces/odoo-relationship.model";
import { OdooSerializableInterface } from "../shared/interfaces/odoo-serializable-interface";
import { MrpProduction } from "./mrp.production.model";
import { ProductProduct } from "./product.product";

export class SaleOrderLine
  extends OdooModel
  implements OdooSerializableInterface<SaleOrderLine>
{
  public ODOO_MODEL = "sale.order.line";
  // qty_delivered_sale: number  = 0
  // confezioni_qty_lorda_delivered: number = 0
  // purchase_line_id: OdooRelationship;
  // fornitore_id: OdooRelationship;
  // purchase_id: OdooRelationship;
  // supplier_purchase_id: OdooRelationship;
  // notes: string = ""
  // working_code: string = ""
  // qty_lorda_so_pz = 0;

  name: string = "";
  bs_sale_order_line = "";
  bs_package_ordered = "";
  bs_package = "";

  create(): SaleOrderLine {
    return new SaleOrderLine();
  }
}
