import { OdooModel } from "../shared/interfaces/odoo-model.model";
import { OdooMultiRelationship } from "../shared/interfaces/odoo-multi-relationship.model";
import { OdooRelationship } from "../shared/interfaces/odoo-relationship.model";
import { OdooSerializableInterface } from "../shared/interfaces/odoo-serializable-interface";
import { StockRule } from "./stock.rule.model";

export class StockLocationRoute extends OdooModel implements OdooSerializableInterface<StockLocationRoute> {
    public readonly ODOO_MODEL = 'stock.route';
    name:string = ""

    rule_ids:  OdooMultiRelationship<StockRule> = new OdooMultiRelationship<StockRule>(StockRule);

    create() {
        return new StockLocationRoute()
    }

}