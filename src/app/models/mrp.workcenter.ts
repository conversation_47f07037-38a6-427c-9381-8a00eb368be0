import { OdooModel } from "../shared/interfaces/odoo-model.model";
import { OdooRelationship } from "../shared/interfaces/odoo-relationship.model";
import { OdooSerializableInterface } from "../shared/interfaces/odoo-serializable-interface";
import { MrpProduction } from "./mrp.production.model";

export class MrpWorkcenter extends OdooModel implements OdooSerializableInterface<MrpWorkcenter> {
    public readonly ODOO_MODEL = 'mrp.workcenter';

    name: string = ""
    
    constructor(id?: number ){ 
        super(id);
     }

     create() {
        return new MrpWorkcenter()
    }
}



// stock.production.lot