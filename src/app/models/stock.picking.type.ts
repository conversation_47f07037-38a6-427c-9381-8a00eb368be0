import { OdooModel } from "../shared/interfaces/odoo-model.model";
import { OdooRelationship } from "../shared/interfaces/odoo-relationship.model";
import { OdooSerializableInterface } from "../shared/interfaces/odoo-serializable-interface";
import { ProductProduct } from "./product.product";
import { StockLocation } from "./stock.location.model";

export class StockPickingType extends OdooModel implements OdooSerializableInterface<StockPickingType> {
    public readonly ODOO_MODEL = 'stock.picking.type';
    // name:string = ""
    name:string = "";
    default_location_dest_id:OdooRelationship<StockLocation> = new OdooRelationship();
    tracking_type:string =  ""

    create() {
        return new StockPickingType()
    }

}