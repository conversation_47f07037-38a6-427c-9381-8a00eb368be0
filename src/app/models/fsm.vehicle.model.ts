import { OdooModel } from "../shared/interfaces/odoo-model.model";
import { OdooRelationship } from "../shared/interfaces/odoo-relationship.model";
import { OdooSerializableInterface } from "../shared/interfaces/odoo-serializable-interface";
import { StockLocation } from "./stock.location.model";

export class FSMVehicle extends OdooModel implements OdooSerializableInterface<FSMVehicle> {
    ODOO_FIELDS?: string[] | undefined;


    public readonly ODOO_MODEL = 'fsm.vehicle';
    name:string = ""
    inventory_location_id:OdooRelationship<StockLocation> = new OdooRelationship<StockLocation>()

    create() {
        return new FSMVehicle()
    }

}