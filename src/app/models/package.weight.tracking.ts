import { OdooModel } from "../shared/interfaces/odoo-model.model";
import { OdooRelationship } from "../shared/interfaces/odoo-relationship.model";
import { OdooSerializableInterface } from "../shared/interfaces/odoo-serializable-interface";
import { MrpProduction } from "./mrp.production.model";
import { MrpWorkcenter } from "./mrp.workcenter";
import { StockQuantPackage } from "./stock.quant.package";

export class PackageWeightTracking extends OdooModel implements OdooSerializableInterface<PackageWeightTracking> {
    public readonly ODOO_MODEL = 'package.weight.tracking';
    tracking_type:string  = "";
    weight: number = 0;
    package_id:OdooRelationship<StockQuantPackage> = new OdooRelationship()
    constructor(id?: number ){ 
        super(id);
     }

     create() {
        return new PackageWeightTracking()
    }
}



// stock.production.lot