import { OdooModel } from "../shared/interfaces/odoo-model.model";
import { OdooRelationship } from "../shared/interfaces/odoo-relationship.model";
import { OdooSerializableInterface } from "../shared/interfaces/odoo-serializable-interface";

export class Partner extends OdooModel implements OdooSerializableInterface<Partner> {
    public readonly ODOO_MODEL = 'res.partner';
    name:string = ""

    create() {
        return new Partner()
    }

}



// stock.production.lot