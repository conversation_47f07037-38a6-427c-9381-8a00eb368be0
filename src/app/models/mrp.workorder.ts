import { OdooModel } from "../shared/interfaces/odoo-model.model";
import { OdooRelationship } from "../shared/interfaces/odoo-relationship.model";
import { OdooSerializableInterface } from "../shared/interfaces/odoo-serializable-interface";
import { MrpProduction } from "./mrp.production.model";
import { MrpWorkcenter } from "./mrp.workcenter";

export class MrpWorkorder extends OdooModel implements OdooSerializableInterface<MrpWorkorder> {
    public readonly ODOO_MODEL = 'mrp.workorder';
    name: string = ""
    production_id : OdooRelationship<MrpProduction> = new OdooRelationship()
    production_state: string = ""
    state : string = ""
    workcenter_id : OdooRelationship<MrpWorkcenter> = new OdooRelationship()
    constructor(id?: number ){ 
        super(id);
     }

     create() {
        return new MrpWorkorder()
    }
}



// stock.production.lot