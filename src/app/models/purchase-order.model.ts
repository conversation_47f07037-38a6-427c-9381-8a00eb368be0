import { OdooModel } from "../shared/interfaces/odoo-model.model";
import { OdooMultiRelationship } from "../shared/interfaces/odoo-multi-relationship.model";
import { OdooRelationship } from "../shared/interfaces/odoo-relationship.model";
import { OdooSerializableInterface } from "../shared/interfaces/odoo-serializable-interface";
import { PurchaseOrderLine } from "./purchase-order-line.model";
import { SaleOrder } from "./sale-order.model";
import { StockPickingType } from "./stock.picking.type";

export class PurchaseOrder extends OdooModel implements OdooSerializableInterface<PurchaseOrder> {
  public readonly ODOO_MODEL = 'purchase.order';

  order_line: OdooMultiRelationship<PurchaseOrderLine>;
  name : string  = ""
  date_order : string = ""
  state : string = ""
  invoice_status : string = ""
  amount_total : string =""
  picking_type_id:OdooRelationship<StockPickingType> = new OdooRelationship<StockPickingType>();

  constructor(id?: number) {
    super(id);
    // this.incoterm = new OdooRelationship();
    this.order_line = new OdooMultiRelationship<PurchaseOrderLine>(PurchaseOrderLine);
  }
  create() {
    return new SaleOrder()
  }

  ODOO_FIELDS?: string[];


}
