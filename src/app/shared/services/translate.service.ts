import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})


export class TranslateService {

  words : Record<string, string>[] = [
    {'done': 'Finito'},
    {'progress': 'In corso'},
    {'ready' : "Pronto"},
    {'pending' : "In attesa di altro OdL"},
    {'to invoice' : "Da fatturare"},
    {'no' : "Nulla da fatturare"},
    {'saleOrder_draft': 'Preventivo'},
    {'saleOrder_sent': 'Preventivo inviato'},
    {'saleOrder_sale': 'Ordine di vendita'},
    {'saleOrder_done': 'Locked'},
    {'saleOrder_cancel': 'Cancellato'},
  ];

  constructor() { }
  translate(word: string){

    for (let index = 0; index < this.words.length; index++) {
      if (this.words[index].hasOwnProperty(word)) 
        return  this.words[index][word] 
    }
    return word
  }
}
