import { Injectable } from '@angular/core';
import {
  Http<PERSON>e<PERSON>,
  <PERSON>ttp<PERSON><PERSON><PERSON>,
  HttpEvent,
  HttpInterceptor,
  HttpErrorResponse,
  HttpResponse
} from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import {tap, switchMap, catchError, finalize, filter, map, first} from 'rxjs/operators';
import {Router} from '@angular/router';
// import { AuthenticationService } from './authentication.service';
import { OdoorpcService } from './odoorpc.service';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  isRefreshingToken: boolean = false;
  tokenSubject: BehaviorSubject<string|null> = new BehaviorSubject<string|null>(null);

  constructor(private router: Router,
    private odoo:OdoorpcService) {
    console.log("AUTH INTERCET")
  }

  
//   addAuthHeader(request) {
//     // const authHeader = this.authService.getAuthorizationHeader();
//     // if (authHeader) {
//         // return request.clone({
//         //     setHeaders: {
//         //         "Authorization": "Bearer"
//         //     }
//         // });
//     // }
//     return request;
// }

  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> { 

    // if (req.responseType == "JSON")

    console.log("intercepet ")
    let c = req.clone()
    var resp:any = next.handle(req).pipe(tap(r => {
      if (r.type != 4 ) 
        return

      if (r.body && r.body.error && r.body.error.code == 100)
      this.router.navigate(["login"])

    }))
    


    // x.toPromise().then(y => {
    //   if (y.body && y.body.error && y.body.error.code == 100) {
    
    //   }
    // })

    return resp
    // x.subscribe(x => {
    //   if (b)
    //   console.log("req22 ", x)
    // })
    // return x
    // return next
    //   .handle(c)
    //   .pipe(
    //     tap(null,
    //       error => {
    //         if(error.status === 401) {
    //             console.log("!!--",error)
    //             this.handle401(req,next)
    //           // this.authService.logout();
    //           // this.router.navigate(['/login']);
    //         }
    //       })
    //     // tap((er: HttpResponse<any>) => {
    //     //   if (er instanceof HttpErrorResponse) {
    //     //     this.handle401(req, next)
    //     //   }
    //     // })
    //   )

    // return next.handle(req).catch(error => {
    //     if (error instanceof HttpErrorResponse) {
    //         switch ((<HttpErrorResponse>error).status) {
    //             case 400:
    //                 return this.handle400Error(error);
    //             case 401:
    //                 return this.handle401Error(req, next);
    //         }
    //     } else {
    //         return Observable.throw(error);
    //     }
    // });

  }


  handle401(req: HttpRequest<any>, next: HttpHandler) {

    // console.log("HANDLE 401")

    // if (!this.isRefreshingToken) {
    //   // this.isRefreshingToken = true

    //   // Reset here so that the following requests wait until the token
    //   // comes back from the refreshToken call.
    //   // this.tokenSubject.next(null);
    //   console.log("REFRESHIIIING TOKEN ")
    //   return this.authService.refreshToken().pipe(map((token) => {
    //     console.log("REFRESH TOKEN ", token)
    //   }))
    // }
  }
}
  