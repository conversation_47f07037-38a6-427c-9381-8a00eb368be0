import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Router, ActivatedRouteSnapshot, RouterStateSnapshot, UrlTree } from '@angular/router';
import { Observable, of } from 'rxjs';
import { QueryDeleteOptions, QueryGetOptions, QueryPostOptions, QueryPutOptions, QuerySearchOptions } from './rest-api.service';


@Injectable({
  providedIn: 'root'
})
export class OdoorpcService  {

  uniqIdCounter: number = 0;
  // odooServer: string = "https://m-dev.galimberti.eu";
  odooServer: string = "";
  params: { db: string; login: string; password: string; } | null;
  logged: boolean = false;

  constructor(
    private httpClient: HttpClient, private router: Router
  ) {
  }

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {

    // this.router.navigate(['/login']);
    return true
  }


  async login(params: { db: string; login: string; password: string; }) {
    console.log("RCP login")
    if (!params)
      return
    this.params = params

    this.logged = true
    // console.log(login, password, this.params)
    var x: any = await this.sendRequest('/api/web/session/authenticate', this.params)
    console.log("RCP ", x)
    if (!x.error) {
      this.logged = true
    } else {
      this.logged = false
    }
    return x
  }

  getSessionInfo() {
    
    return  this.sendRequest('/api/web/session/get_session_info',{})
  }

  private buildRequest(params: any, id?) {
    this.uniqIdCounter += 1;

    return JSON.stringify({
      jsonrpc: '2.0',
      method: 'call',
      id,
      params,
    });
  }


  async delete(opt: QueryDeleteOptions) {
    var kwargs = kwargs || {};
    kwargs.context = [];
    // Object.assign(kwargs.context, this.context);

    const params = {
      model: opt.table,
      method: "unlink",
      args: [opt.ids],
      kwargs: {},
    };

    var r: any = await this.sendRequest('/api/web/dataset/call_kw', params);
    console.log("LINK post", r)
    return r.result
  }


  async onchange3(model, id, fields): Promise<any> {


    var kwargs = kwargs || {};
    kwargs.context = [];
    const params = {
      model: model,
      method: "onchange",
      args: [[], { 'product_id': 2 }, "product_id", { 'product_id': "1" }],
      kwargs: {
        "context": {
          "lang": "it_IT"
        }
      },
    };



    // if (fields) {
    //   params.args.push(fields)
    // }

    var r: any = await this.sendRequest('/api/web/dataset/call_kw/' + model + "/onchange", params)

    return r.result ? r.result : []
  }





  async call(method, model, value, id?, fields?): Promise<any> {
    var kwargs = kwargs || {};
    kwargs.context = [];
    const params = {
      model: model,
      method: method,
      args: [value],
      kwargs: {
        "context": {
          "lang": "it_IT"
        }
      }
    };

    if (fields) {
      params.args.push(fields)
    }

    var r: any = await this.sendRequest('/api/web/dataset/call_kw/' + model + "/" + method, params, id)
    console.log("rr", r)

    if (r.error) {
      throw r.error.data.message
    }

    return r.result ? r.result : []
  }

  async sendRequest(url: string, params: {}, id?): Promise<Object | undefined> {

    var body = this.buildRequest(params, id);

    var headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Coral-Communication-Mode': 'restful', // Needed to differenciate error handling odoo side
      // 'X-Openerp-Session-Id': this.localstorageSessionServivce.getSessionId(),
      // Authorization: 'Basic ' + btoa(this.httpAuth)
    });

    try {

      var x = await this.httpClient.post(this.odooServer + url, body, { headers: headers }).toPromise()
      return x
      // return await this.httpClient.post(this.odooServer + url, body, {headers: headers}).toPromise()
    } catch (error) {

      this.router.navigate(["login"])
      return {}
    }
  }


  async searchRead(options: QuerySearchOptions): Promise<any[]> {

    var params = {
      context: {
        "lang": "it_IT"
      },
      domain: options.criteria,
      fields: options.fields,
      limit: 2000,
      model: options.table,
      offset: 0,
      sort: options.order
    };

    var r: any = await this.sendRequest('/api/web/dataset/search_read', params)
    console.log("rrrrrr ")
    return r.result ? r.result.records : []
  }


  async post(queryPostOptions: QueryPostOptions): Promise<number[]> {
    console.log("xxcreate post",)
    var kwargs = kwargs || {};
    kwargs.context = [];
    // Object.assign(kwargs.context, this.context);

    const params = {
      model: queryPostOptions.table,
      method: "create",
      args: [[queryPostOptions.json_fields]],
      kwargs: {},
    };
    console.log("xxcall_kw ", params)
    var r: any = await this.sendRequest('/api/web/dataset/call_kw', params);
    console.log("xxcreate dopo post",)
    console.log("LINK post", r)
    return r.result
  }

  async onchange(model, jsonfields) {
    var kwargs = kwargs || {};
    kwargs.context = [];
    const params = {
      model: model,
      method: "call",
      args: [[], {}, [], jsonfields],
      kwargs: kwargs,
    };

    var r: any = await this.sendRequest('/api/web/dataset/call_kw', params);
    return r
  }


  async put<T>(queryPutOptions: QueryPutOptions): Promise<T[]> {

    var kwargs = kwargs || {};
    kwargs.context = [];
    // Object.assign(kwargs.context, this.context);

    const params = {
      model: queryPutOptions.table,
      method: "write",
      args: [[(Number(queryPutOptions.id))], queryPutOptions.json_fields],
      kwargs: {},
    };




    var r: any = await this.sendRequest('/api/web/dataset/call_kw', params);
    return r

    //   var params = {
    //     context: {},
    //     domain: [],
    //     // fields: options.fields,
    //     limit: 10,
    //     model: queryPutOptions.table,
    //     offset: 0,
    //     sort: ""
    // };

    // var r:any = await this.sendRequest('/api/web/dataset/get', params)

    // return r.result ? r.result.records : []
  }


}






