import { Injectable } from '@angular/core';
import {
  OnChangeOptions,
  QueryGetOptions,
  QueryPostOptions,
  QueryPutOptions,
  RestapiService
} from './rest-api.service';
import { firstValueFrom, from, Observable, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { OdooSerializableInterface } from '../interfaces/odoo-serializable-interface';
import { OdooMultiRelationship } from '../interfaces/odoo-multi-relationship.model';
import { OdoorpcService } from './odoorpc.service';
import { OdooRelationship } from '../interfaces/odoo-relationship.model';
import { MrpProduction } from 'src/app/models/mrp.production.model';
import { HttpClient, HttpHeaders } from '@angular/common/http';

@Injectable({
  providedIn: 'root'
})
export abstract class OdooEntityManager {
  static readonly DEFAULT_LIMIT = 200;

  constructor(
    private httpClient: HttpClient,
    private restApiService: RestapiService,
    private odoorpcService: OdoorpcService
  ) {
  }

  getSessionInfo() {
    return  from(this.odoorpcService.getSessionInfo())
  }

  search<T>(serializedObj: OdooSerializableInterface<T>, criteria:any[] = [],
            limit: number = OdooEntityManager.DEFAULT_LIMIT, extra?: string, order?: string): Observable<T[]> {
    const querySearchOptions = this.inputValuesToQuerySearchOptions(serializedObj.ODOO_MODEL,
      criteria, limit, extra, order, serializedObj.fields()
      );

    return from(this.odoorpcService.searchRead(querySearchOptions))
      .pipe(
        map(response => {
          return Array.isArray(Object.values(response)[0]) ? Object.values(response)[0] : Object.values(response);
        }),
        map(response => {
          var x = response.map((res:any) => serializedObj.deserialize(res));
          return x
        }),
      );
  }

  // get<T>(serializedObj: OdooSerializableInterface<T>, id: number, fields = null): Observable<T> {
  //   const queryGetOptions = this.inputValuesToQueryGetOptions(serializedObj.ODOO_MODEL, id.toString(), fields);
  //   return from(this.odoorpcService.get(queryGetOptions)).pipe(
  //     map(response => {
  //       return serializedObj.deserialize(response);
  //     }));
  // }

  update<T>(serializedObj: OdooSerializableInterface<T>, jsonFields: object): Observable<T | null> {
    const queryPutOptions = this.inputValuesToQueryPutOptions(serializedObj.ODOO_MODEL, serializedObj.id, jsonFields);
    return from(this.odoorpcService.put(queryPutOptions)).pipe(
      map(response => {
        return serializedObj.deserialize(response);
      })
    );
  }

  // We need double parameter since we don't have the same object type between the new one and the serialized, we need id for association
  create<T>(serializedObj: OdooSerializableInterface<T>, newObj: any): Observable<T | null> {
    const queryPostOptions = this.inputValuesToQueryPostOptions(serializedObj.ODOO_MODEL, newObj);
    return from(this.odoorpcService.post(queryPostOptions)).pipe(
      map(response => {
        console.log("LINK PIPE" ,response)
        // update id from response
        newObj.id = response[0]

        return serializedObj.deserialize(newObj);
      })
    );
  }


  create2<T>(serializedObj: OdooSerializableInterface<T>, newObj: any): Observable<T | null> {
    const queryPostOptions = this.inputValuesToQueryPostOptions(serializedObj.ODOO_MODEL, newObj);
    return from(this.odoorpcService.call("create", serializedObj.ODOO_MODEL , newObj )).pipe(
      map(response => {
        console.log("LINK PIPE" ,response)
        // update id from response
        newObj.id = response[0]

        return serializedObj.deserialize(newObj);
      })
    );
  }




  unlinkToMulti<T>(serializedParentObj: OdooSerializableInterface<T>, removeFromKey: string, idToRemove: number): Observable<T| null> {
    return this.update<T>(serializedParentObj, {[removeFromKey]: [[3, idToRemove]]})
      .pipe(catchError(() => of(null)),
        map(res => {
          if (!res) {
            return null;
          }

          

          serializedParentObj[removeFromKey].ids = serializedParentObj[removeFromKey].ids.filter((id:number) => id !== idToRemove);
          serializedParentObj[removeFromKey].values = serializedParentObj[removeFromKey].values.filter(value => value.id !== idToRemove);
          return serializedParentObj as unknown as T;
        }));
  }

  linkToMulti<T>(serializedParentObj: OdooSerializableInterface<T>, addToKey: string, objectToAdd: any): Observable<T | null> {
    return this.update<T>(serializedParentObj, {[addToKey]: [[4, objectToAdd.id]]})
      .pipe(catchError(() => of(null)),
        map(res => {
          if (!res) {
            return null;
          }
          serializedParentObj[addToKey].ids.push(objectToAdd.id);
          serializedParentObj[addToKey].values.push(objectToAdd);
          return serializedParentObj as unknown as T;
        }));
  }

  setRelation<T, U = T>(serializedParentObj: OdooSerializableInterface<T>, addToKey: string, objectToAdd: OdooSerializableInterface<U>): Observable<T | null> {
    return this.update<T>(serializedParentObj, {[addToKey]: objectToAdd.id})
      .pipe(catchError(() => of(null)),
        map(res => {
          if (!res) {
            return null;
          }
          serializedParentObj[addToKey] = res[addToKey];
          return serializedParentObj as unknown as T;
        }));
  }

  createAndLinkToMulti<T>(serializedParentObj: OdooSerializableInterface<T>, addToKey: string, newObject: object): Observable<T | null> {
    return this.update<T>(serializedParentObj, {[addToKey]: [[0, 0, newObject]]})
      .pipe(catchError(() => of(null)),
        map(res => {
          if (!res) {
            return null;
          }
          serializedParentObj[addToKey] = res[addToKey];
          return serializedParentObj as unknown as T;
        }));
  }

  // Generic delete
  delete<T>(serializedObj: OdooSerializableInterface<T>, ids: number[]): Observable<T> {
    return from(this.odoorpcService.delete({table: serializedObj.ODOO_MODEL, ids: ids.map(id => Number(id)) })).pipe(
      map(response => {
        return serializedObj.deserialize(response);
      })
    );
  }

  // Delete From MultiRelationShip
  deleteMulti<T>(serializedObj: OdooMultiRelationship<T>, idsToRemove: number[]): Observable<T | null> {
    return from(this.odoorpcService.delete({
      table: new serializedObj.typeOfObj().ODOO_MODEL,
      ids: idsToRemove.map(id => Number(id))
    })).pipe(catchError(() => of(null)),
      map(res => {
        if (!res) {
          return null;
        }
        serializedObj.ids = serializedObj.ids.filter(id => {
          return !idsToRemove.includes(id);
        });
        // @ts-ignore
        // serializedObj.values = serializedObj.values.filter(value => !idsToRemove.includes(value.id));
        return serializedObj as unknown as T;
      }));
  }

  resolve<T>(childToResolve: OdooMultiRelationship<T>): Observable<OdooMultiRelationship<T>> {
    if (!childToResolve.ids.length) {
      childToResolve.values = [];
      return of(childToResolve);
    }
    const newInstanceObj = new childToResolve.typeOfObj();
    return this.search<T>(newInstanceObj, [['id', 'in', childToResolve.ids]]).pipe(
      catchError((e) => []),
      map(response => {
        childToResolve.values = response;
        return childToResolve;
      })
    );
  }

  resolveSingle<T>(serializedObj, childToResolve: OdooRelationship<T>): Observable<OdooRelationship<T>> {
    if (!childToResolve.id) {
      // childToResolve.value = null;
      return of(childToResolve);
    }
    
    // const newInstanceObj = new childToResolve.typeOfObj();
    return this.search<T>(serializedObj, [['id', '=', childToResolve.id]]).pipe(
      catchError((e) => []),
      map(response => {
        childToResolve.value = response[0];
        return childToResolve;
      })
    );
  }


  
  /** USE WITH CAUTION. */
  // resolveAll<T>(serializedOriginalObj: T): Observable<T | null> {
  //   const hasRelationShip = (variableToCheck: any): variableToCheck is OdooMultiRelationship<T> => {
  //     const ids = (variableToCheck as OdooMultiRelationship<T>).ids;
  //     return ids !== undefined && ids.length > 0;
  //   };
  //   const requests = [];
  //   Object.keys(serializedOriginalObj).forEach(key => {
  //     if (hasRelationShip(serializedOriginalObj[key])) {
  //       if (!serializedOriginalObj[key].ids.length) {
  //         serializedOriginalObj[key].values = []; // Avoid a call if no ids
  //         return of(serializedOriginalObj);
  //       }
  //       const newInstanceObj = new serializedOriginalObj[key].typeOfObj();

  //       requests.push(this.search<T>(newInstanceObj,null).pipe(
  //         catchError(() => of([])),
  //         map(response => {
  //           // Map each item of the response
  //           serializedOriginalObj[key].values = response;
  //           return serializedOriginalObj;
  //         })
  //       ));
  //     }
  //   });

  //   if (requests.length > 0) {
  //     return forkJoin(requests).pipe(map(result => result[0])) as Observable<T>;
  //   } else {
  //     return of(serializedOriginalObj);
  //   }
  // }


  resolveArrayOfSingle<T, U = T>(outputObj: OdooSerializableInterface<T>, serializedOriginalArrayObj: U[], key: string): Observable<U[]> {
    var idsTosend : number[] = [];
    serializedOriginalArrayObj.forEach(serializedOriginalObj => {
      idsTosend.push(serializedOriginalObj[key].id);
      if (!serializedOriginalObj[key].value) {
        serializedOriginalObj[key].value = [];
      }
    });
    idsTosend = idsTosend.filter(i => i)
    console.log("out" ,serializedOriginalArrayObj)
    // Avoid useless calls
    if (!idsTosend.length) {
      return of(serializedOriginalArrayObj);
    }

    console.log("out" , outputObj, idsTosend)
    return this.search<T>(outputObj, [['id', 'in', idsTosend]]).pipe(
      catchError(() => of([])),
      map(response => {
        // Map each item of the response to the corresponding object
        const responseMapTmpObj = {};
        response.forEach((singleObj:any) => {
          responseMapTmpObj[singleObj.id] = singleObj;
        });

        serializedOriginalArrayObj.forEach(serializedOriginalObj => {

          console.log("filling ",serializedOriginalObj[key], responseMapTmpObj[serializedOriginalObj[key].id])
          serializedOriginalObj[key].value = responseMapTmpObj[serializedOriginalObj[key].id]
          console.log("....",serializedOriginalArrayObj, responseMapTmpObj)
          // serializedOriginalObj[key].value = responseMapTmpObj[id]
          // serializedOriginalObj[key].id.forEach(id => {
          //   serializedOriginalObj[key].values.push(responseMapTmpObj[id]);
          // });
        });
        return serializedOriginalArrayObj;
      })
    );
  }



  /** THIS IS HEAVY, USE WITH CAUTION. */
  resolveArray<T, U = T>(outputObj: OdooSerializableInterface<T>, serializedOriginalArrayObj: U[], key: string): Observable<U[]> {
    var idsTosend = [];
  
    serializedOriginalArrayObj.forEach(serializedOriginalObj => {
      idsTosend = idsTosend.concat(serializedOriginalObj[key].ids);
      if (!serializedOriginalObj[key].values) {
        serializedOriginalObj[key].values = [];
      }
    });


    // Avoid useless calls
    // if (!idsTosend.length) {
    //   return of(serializedOriginalArrayObj);
    // }



    return this.search<T>(outputObj, [['id', 'in', idsTosend]]).pipe(
      catchError(() => of([])),
      map(response => {

        // Map each item of the response to the corresponding object
        const responseMapTmpObj = {};

        response.forEach((singleObj:any) => {
          responseMapTmpObj[singleObj.id] = singleObj;
        });

        serializedOriginalArrayObj.forEach(serializedOriginalObj => {
          serializedOriginalObj[key].ids.forEach(id => {
            if (serializedOriginalObj[key].values.indexOf(responseMapTmpObj[id]) == -1)
              serializedOriginalObj[key].values.push(responseMapTmpObj[id]);
          });
        });

        return serializedOriginalArrayObj;
      })
    );
  }


  async onChange2<T>(serializedObj: OdooSerializableInterface<T>, id, field,value) {
    var o = {}
    o[field] = value

    
    return this.odoorpcService.onchange3(serializedObj.ODOO_MODEL, id,o)
  }

  // onChange<T>(serializedObj: OdooSerializableInterface<T>, id: number, field: string, value: any): Observable<T> {
  //   const queryOnChangeOptions = this.inputValuesToOnChangeOptions(serializedObj.ODOO_MODEL, id, field, value);
  //   return from(this.restApiService.onChange(queryOnChangeOptions)).pipe(
  //     map(response => {
  //       return serializedObj.deserialize(response);
  //     })
  //   );
  // }


  call2<T>(serializedObj: OdooSerializableInterface<T>, method: string, params: any, id?: string|null,value?:any): Observable<any> {

    console.log( "value ", value)
    return from(this.odoorpcService.call(
      method,
      serializedObj.ODOO_MODEL,
      value,
      id,
      params
    )).pipe(
      map(res => {
        console.log("res", res)


        // if (res && res.message) {
        //   return res;
        // }
        // console.log("LOHG CALL ", res);
        // if (res && res.id)
        //   return serializedObj.deserialize(res);
        return res
      })
    );
  }

  call<T>(serializedObj: OdooSerializableInterface<T>, method: string, params: any, id?: string|null,value?:any): Observable<any> {

    console.log( "value ", value)
    return from(this.odoorpcService.call(
      method,
      serializedObj.ODOO_MODEL,
      value,
      id,
      params
    )).pipe(
      map(res => {
        console.log("res", res)

        // if (res && res.message) {
        //   return res;
        // }
        // console.log("LOHG CALL ", res);
        // // if (res && res.id)
        // //   return serializedObj.deserialize(res);
        return res
      })
    );
  }


  async call27(model, method, args, context?,id?) {

    var headers = new HttpHeaders({
        'Content-Type': 'application/json',
        // 'Coral-Communication-Mode': 'restful', // Needed to differenciate error handling odoo side
    });

    var url = '/api/web/dataset/call_kw/' + model + "/" + method;

    let params = {
      model: model,
      method: method,
      args: args,
      kwargs: {
        context: context
      },
    };

    let body = JSON.stringify({
      jsonrpc: '2.0',
      method: 'call',
      params,
    });


  return await firstValueFrom<any>(await this.httpClient.post(this.odoorpcService.odooServer + url, body, {headers: headers}))
}

  // callArray<T>(serializedObj: OdooSerializableInterface<T>, method: string, params: string, id?: string): Observable<any> {
  //   return from(this.restApiService.call({
  //     table: serializedObj.ODOO_MODEL,
  //     method,
  //     params,
  //     id
  //   })).pipe(
  //     catchError(() => of(null)),
  //     map(res => {
  //       if (!res || res.message) {
  //         return null;
  //       }
  //       return res.map(single => serializedObj.deserialize(single));
  //     })
  //   );
  // }

  // webhook(id: string, name: string, email: string): Observable<any> {
  //   return from(this.restApiService.webhook({id, name, email}));
  // }


  inputValuesToQuerySearchOptions(
    model: string,
    domain?: any[][],
    limit?: number,
    extra?: string,
    order?: string,
    fields?: string[],
  ): any {


    const optionObj = {
      table: model,
      criteria : domain,
      limit,
      extra,
      sort : order,
      fields,
    };

    console.log("SEACRH ", optionObj)
    // if (domain) {
    //   // domain.forEach(el => {
    //   //   if (el.length === 1) {
    //   //     // OR case
    //   //     optionObj.criteria.push(new QueryCriteriaOR());
    //   //   } else {
    //   //     optionObj.criteria.push({
    //   //       column: el[0],
    //   //       operator: el[1],
    //   //       value: el[2],
    //   //       type_column: el[3] ? el[3] : null
    //   //     });
    //   //   }
    //   // });
    //   optionObj.criteria = domain
    // }
    return optionObj;
  }

  inputValuesToOnChangeOptions(
    table: string,
    id: number,
    field: string,
    value: string | number
  ): OnChangeOptions {
    return {
      id: id.toString(),
      table,
      field,
      value
    };
  }

  inputValuesToQueryGetOptions(model: string, id: string, fields: any): QueryGetOptions {
    return {
      table: model,
      id,
      fields
    };
  }

  inputValuesToQueryPutOptions(model: string, id: number, jsonFields: object): QueryPutOptions {
    return {
      table: model,
      id: id.toString(),
      json_fields: jsonFields
    };
  }

  inputValuesToQueryPostOptions(model: string, jsonFields: object): QueryPostOptions {
    return {
      table: model,
      json_fields: jsonFields,
    };
  }
}
