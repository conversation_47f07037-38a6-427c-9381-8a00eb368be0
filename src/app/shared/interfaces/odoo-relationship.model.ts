
export class OdooRelationship<T = void> {
    id: number;
    name: string;
    value:T 

    constructor(id?: number, name?: string) {
        if (id)
            this.id = id;
        if (name)
            this.name = name;
    }

    static deserialize(input: any) {
        try {
            return new OdooRelationship(
                input[0],
                input[1]
            );
        } catch (e) {
            return null;
        }
    }
}
