import { AfterViewInit, Component, Input, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { firstValueFrom } from 'rxjs';
import { CONFIG } from 'src/app/CONFIG';
import { StockLocationRoute } from 'src/app/models/stock.location.route.model';
import { StockPicking } from 'src/app/models/stock.picking';
import { StockRule } from 'src/app/models/stock.rule.model';
import { OdooEntityManager } from 'src/app/shared/services/odoo-entity-manager.service';

@Component({
  selector: 'app-nav',
  templateUrl: './nav.component.html'
})
export class NavComponent implements OnInit {
  // @Input() picking: StockPicking
  stockLocationRoute: StockLocationRoute
  arrayStockPicking: StockPicking[];
  options: any[];
  selected : any
  username: ""

  constructor(
    private router : Router,
    private odooEM : OdooEntityManager
  ) { }


  async ngOnInit(): Promise<void> {
    console.log("II")
    var si:any = await firstValueFrom(this.odooEM.getSessionInfo())
    
    this.username = si.result.username
    // TODO giulio
    // var r = await firstValueFrom(this.odooEM.search<StockLocationRoute>(new StockLocationRoute(), [["id", "=", CONFIG.reception_route_id]]))
    // this.stockLocationRoute = r[0]
    // await this.odooEM.resolve(this.stockLocationRoute.rule_ids).toPromise()
  }

  
  
  
   

  goesTo() {
    if( this.selected ){

      console.log("NAVIGA ", this.selected)
      this.router.navigate([
        this.selected
      ]);
    }
  }

}

