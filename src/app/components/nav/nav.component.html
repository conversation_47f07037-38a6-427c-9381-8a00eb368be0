
<nav class="navbar  navbar-expand-lg bg-light">
  <div class="container-fluid">
    <a class="navbar-brand" href="#">
      <img width="60%" src="/assets/logo-bassi-1890.png" />
    </a>
    
    
      
        <div class="dropdown ms-auto me-3">

          
          <button class="btn btn-link " type="button" data-bs-toggle="dropdown" aria-expanded="false">
            <i class="fa fa-2x fa-bars text-primary"></i>
          </button>
          <ul class="dropdown-menu dropdown-menu-end">
            
            <div class="p-3 text-center text-muted">
              <div>
                <i class="fa fa-user text-muted"></i><br>
                {{username}}
              </div>
              <button class="btn btn-primary mt-3" [routerLink]="['/login']">Logout</button>
            </div>
            
            <li><hr class="dropdown-divider"></li>
            <li><a class="py-3 dropdown-item" [routerLink]="['/production']" >Produzione</a></li>
            <li><hr class="dropdown-divider"></li>

            <li *ngFor="let r of stockLocationRoute?.rule_ids?.values" class=""  >
              <!-- TODO GIULIO  -->
              <!-- <a class="py-3  dropdown-item" [routerLink]="['/warehouse/pickings',r.id]">{{r.location_id.name.split("/").slice(-1) }} </a> -->
            </li>
            <!-- <li><a class="py-3 dropdown-item" [routerLink]="['/sales/']"  >Ordini di vendita</a></li> -->
            
          </ul>
        </div>
        
<!--         
        <ul class="navbar-nav">
          <li class="nav-item pt-1 me-3">
            
          </li>
        </ul> -->
    </div>
</nav>

<!--
<div class="container-fluid">

    <div class="row">
        <ul class="nav">
            <li class="nav-item">
              <a class="nav-link active" aria-current="page" href="#">LOGO</a>
            </li>
            <li class="nav-item">
                <input type="date" class="form-control"  placeholder="hh:mm - 01/01/2022">
            </li>
            <li class="nav-item">
                <input type="text" class="form-control"  placeholder="Nome operatore">
            </li>
            <li class="nav-item">
              <a class="nav-link disabled">ITA / ENG</a>
            </li>
        
            <li class="nav-item">
                <a class="nav-link disabled">exit</a>
              </li>
          </ul>
    

    </div>
   
</div> -->

