import { AfterViewInit, Component, ElementRef, EventEmitter, OnInit, OnDestroy, Output } from '@angular/core';
import { Socket, io } from 'socket.io-client';

@Component({
  selector: 'app-weigh-input',
  templateUrl: './weigh-input.component.html',
  styleUrls: ['./weigh-input.component.scss']
})
export class WeighInputComponent implements OnInit, AfterViewInit, OnDestroy {

  weight: string = "";
  @Output() onWeight: EventEmitter<string|null> = new EventEmitter();
  private socket: Socket;
  isManualMode: boolean = false;
  connectionStatus: 'connected' | 'disconnected' | 'connecting' = 'connecting';
  lastUpdate: Date | null = null;
  visible = true;

  constructor(private el: ElementRef) {
    // Connessione al server Socket.IO

  }

  private setupSocketListeners() {
    this.socket.on('connect', () => {
      this.connectionStatus = 'connected';
    });

    this.socket.on('disconnect', () => {
      this.connectionStatus = 'disconnected';
    });

    this.socket.on('connect_error', () => {
      this.connectionStatus = 'disconnected';
    });
  }

  ngAfterViewInit(): void {
    // var m = this.el.nativeElement.querySelector('.modal')



    // m.addEventListener('hidden.bs.modal', e => {
    //   m.removeEventListener('hidden.bs.modal')
      
    //   if(this.weight) {
    //     this.onWeight.emit(this.weight)
    //   }
    // })
  }

  onDialogHidden() {
    this.onWeight.emit(null);
  }

  ngOnInit(): void {

    this.socket = io('http://192.168.1.111:1300', {
      transports: ['websocket']
    });
    this.setupSocketListeners();


    // Ascolta gli aggiornamenti del peso dal server
    this.socket.on('weight_update', (data: any) => {
      if (data.source === 'bilancia 1 - .36' && !this.isManualMode) {
        console.log(data)
        this.weight = data.weight.toString();
        this.lastUpdate = new Date();
      }
    });
  }

  ngOnDestroy(): void {
    if (this.socket) {
      this.socket.disconnect();
    }
  }

  getStatusColor(): string {
    switch (this.connectionStatus) {
      case 'connected':
        return 'text-success';
      case 'disconnected':
        return 'text-danger';
      case 'connecting':
        return 'text-warning';
      default:
        return 'text-secondary';
    }
  }

  getStatusText(): string {
    switch (this.connectionStatus) {
      case 'connected':
        return 'Connesso';
      case 'disconnected':
        return 'Disconnesso';
      case 'connecting':
        return 'In connessione';
      default:
        return '';
    }
  }

  getLastUpdateText(): string {
    if (!this.lastUpdate) return 'Nessun aggiornamento ricevuto';
    const now = new Date();
    const diff = now.getTime() - this.lastUpdate.getTime();
    const seconds = Math.floor(diff / 1000);
    
    if (seconds < 60) return `Ultimo aggiornamento: ${seconds}s fa`;
    const minutes = Math.floor(seconds / 60);
    return `Ultimo aggiornamento: ${minutes}m fa`;
  }

  digit(i: string) {
    this.isManualMode = true;
    this.weight = this.weight + i;
  }

  back() {
    if (this.weight.length > 0) {
      this.isManualMode = true;
      this.weight = this.weight.slice(0, -1);
    }
  }

  clear() {
    this.weight = "";
    this.isManualMode = true;
  }

  toggleMode() {
    this.isManualMode = !this.isManualMode;
    if (!this.isManualMode) {
      // Reset per permettere aggiornamenti automatici
      this.weight = "";
    }
  }

  confirm() {
    this.onWeight.emit(this.weight);
  }
}