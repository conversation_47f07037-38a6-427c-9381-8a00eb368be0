<p-dialog 
  header="Bilancia 1" 
  [modal]="true" 
  [(visible)]="visible" 
  (onHide)="onDialogHidden()"
  [style]="{ width: '35rem' }">

   <!-- Stato Connessione -->
   <div class="d-flex justify-content-between align-items-center px-3 py-2 bg-light border-bottom">
    <div class="d-flex align-items-center">
      <i class="fa fa-circle me-2" [ngClass]="getStatusColor()"></i>
      <span class="small">{{ getStatusText() }}</span>
    </div>
    <span class="small text-muted">{{ getLastUpdateText() }}</span>
  </div>

  <!-- Input Peso -->
  <div class="d-flex justify-content-between align-items-center p-3">
    <input class="form-control border-0 fs-1" [(ngModel)]="weight">
    <span class="fs-2 ms-2">kg</span>
  </div>

  <!-- Controlli -->
  <div class="d-flex justify-content-between px-3">
    <button class="btn btn-outline-secondary btn-sm" (click)="toggleMode()">
      {{ isManualMode ? 'Modalità Automatica' : 'Modalità Manuale' }}
    </button>
    <button class="btn btn-outline-secondary btn-sm" (click)="clear()">
      Cancella
    </button>
  </div>

  <!-- Tastierino Numerico -->
  <table class="table mb-0 table-bordered w-100 text-center mt-3">
    <tbody>
      <tr>
        <td style="width:33%" (click)="digit('1')">1</td>
        <td style="width:33%" (click)="digit('2')">2</td>
        <td style="width:33%" (click)="digit('3')">3</td>
      </tr>
      <tr>
        <td (click)="digit('4')">4</td>
        <td (click)="digit('5')">5</td>
        <td (click)="digit('6')">6</td>
      </tr>
      <tr>
        <td (click)="digit('7')">7</td>
        <td (click)="digit('8')">8</td>
        <td (click)="digit('9')">9</td>
      </tr>
      <tr>
        <td (click)="digit('.')">.</td>
        <td (click)="digit('0')">0</td>
        <td (click)="back()"><i class="fa fa-lg fa-delete-left"></i></td>
      </tr>
    </tbody>
  </table>
  <button class="btn btn-primary m-3" (click)="confirm()">Conferma</button>
</p-dialog>


