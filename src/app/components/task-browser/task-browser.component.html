<div class="table-responsive">
  <table class="table w-100 ">
    <thead>
      <tr class="text-nowrap" >
        <th class="">Lotto</th>
        <th *ngFor="let r of filterPicking(stockLocationRoute?.rule_ids?.values)" class="text-center text-uppercase"> 
          <ng-container > 
          {{r.picking_type_id.name.split(": ")[1].slice(0,1)}}
          <a [routerLink]="['/warehouse/pickings',r.picking_type_id.id]"></a>
          </ng-container>
        </th>
      </tr>
    </thead>
    <tbody class="">
      <ng-container *ngFor="let pr of productions">
        <tr  > 
          <td class="text-nowrap ps-3"  class="" scope="row">{{pr.name}}</td>
          <!-- <td>{{p.scheduled_date | date}}</td> -->
          <td *ngFor="let p of filterPicking(pr.picking_ids.values).reverse()"  [ngClass]="getClassByValue(p)"  style="display: revert;" [routerLink]="['picking/' + p.id]" >
            <!-- {{getMove(p.origin, r)?.picking_type_id?.value}}
            {{getMove(p.origin, r)?.id}} -->
            &nbsp;

          </td>
        </tr>
      </ng-container>
    </tbody>
  </table>
</div>
