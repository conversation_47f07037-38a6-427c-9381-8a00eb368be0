import { AfterViewInit, Component, Input, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { CONFIG } from 'src/app/CONFIG';
import { MrpProduction } from 'src/app/models/mrp.production.model';
import { StockLocationRoute } from 'src/app/models/stock.location.route.model';
import { StockMove } from 'src/app/models/stock.move';
import { StockPicking } from 'src/app/models/stock.picking';
import { StockRule } from 'src/app/models/stock.rule.model';

@Component({
  selector: 'app-task-browser',
  templateUrl: './task-browser.component.html'
})
export class TaskBrowserComponent implements AfterViewInit {

  @Input() productions: MrpProduction[] 
  @Input() stockLocationRoute: StockLocationRoute
  // @Input() arrayStockMove: StockMove[]

  constructor(
    private router: Router
  ) { }


  ngAfterViewInit(): void {
    console.log("STOCK LOCATION ROUTE", this.stockLocationRoute)
  }

  ngOnInit(): void {

  }


  filterPicking(list:any|undefined|null) {
    console.log("filter picking",list)
    if (list )
      console.log("LIST " , list)
      return list
      // return list.filter(x => !CONFIG.picking_types_filtered.includes(x.picking_type_id.id))
    return
    []
  }

  // showRow(sp:StockPicking){
  //   var show = false
  //   // console.log("sp",sp)

  //   for (let index = 0; index < this.arrayStockMove.length; index++) {
  //     const m = this.arrayStockMove[index];
  //     if(sp.origin == m.origin && m.state != 'cancel' && m.state != 'done' && m.picking_type_id.id != 5)
  //       show = true
  //   }
  //   return show
  // }
 
  // getMove(origin:string,r:StockRule) {
  //   var res = this.arrayStockMove.filter(m => {
  //     // if (m.origin == origin)
  //     //   console.log("PARA",m.picking_id.name, m.picking_type_id.id ,r.picking_type_id.id)
  //     return m.origin == origin && m.picking_type_id.id == r.picking_type_id.id
  //   }).reverse()
  //   if (res)
  //     return res[0]
  //   return null
    
  // }
  getState(p: StockPicking, r: StockRule) : any {
    return p.state
    // return p.state
    // if(!r) return ""
    // var obj = {"state" : "", p_id : 0}

    // for (let index = 0; index < this.arrayStockMove.length; index++) {
    //   const m = this.arrayStockMove[index];
    //   if(p.origin != m.origin) continue;

    //   if ( m.location_dest_id.name == r.location_id.name){
    //     obj.state = m.state
    //     obj.p_id = m.picking_id.id
    //     break
    //   }
    // }
    // return obj
  }


  getClassByValue(s:StockPicking) {
    switch (s.state) {
      case "done": return "bg-success";
      case "assigned": return "bg-warning";
      case "waiting": return "bg-danger";
      case "progress": return "bg-success progress-bar progress-bar-striped progress-bar-animated";
      // case "cancel": return "bg-danger";
      default: return ""
    }
  }

  // translate(value) {
  //   switch (value) {
  //     case "done": return "CONCLUSO";
  //     case "pending": return "prossimo"
  //     case "ready": return "INIZIA";
  //     case "progress": return "AVVIATO"
  //     default: return ""
  //   }
  // }
}