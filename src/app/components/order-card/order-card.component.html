
<div class="card m-3" *ngIf="!s">

    <div class="card-header">Intervento in corso</div>

    <div class="card-body text-center">
        <p class="lead">Nessun intervento in corso</p>
        <button class="btn btn-primary" type="button" (click)="isPickingOrder = true">Inzia</button>
    </div>
</div>

<div class="card m-3" *ngIf="s">

    <div class="card-header">Intervento in corso</div>

    <div class="card-body text-center">
        
        <p class="lead ">{{s.location_id.value.owner_id.name}}
            <br>{{s.location_id.value.street}} - {{s.location_id.value.city}}
        </p>

        <p class="lead text-muted">{{s.name}}&nbsp; 
            <span class="badge bg-light text-dark">{{s.stage_id.name}}</span>
        </p>

        <div class="mt-5">
            <button class="btn btn-light me-3" (click)="cancel()">
                 <PERSON><PERSON>
            </button>
            <button class="btn btn-primary" (click)="open()">Apri</button>
        </div>
    </div>
</div>

<app-order-picker *ngIf="isPickingOrder" (onOrder)="onOrder($event)"></app-order-picker>