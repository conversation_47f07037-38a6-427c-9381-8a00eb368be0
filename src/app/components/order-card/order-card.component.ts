import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { firstValueFrom } from 'rxjs';
import { FSMLocation } from 'src/app/models/fsm.location';
import { FSMOrder } from 'src/app/models/fsm.oder.model';
import { OdooEntityManager } from 'src/app/shared/services/odoo-entity-manager.service';


@Component({
  selector: 'app-order-card',
  templateUrl: './order-card.component.html'
})
export class OrderCardComponent implements OnInit {
  
  s:FSMOrder|null
  isPickingOrder:boolean = false


  constructor(
    public odooEM:OdooEntityManager,
    public router:Router
  ) { }
  
  async ngOnInit(): Promise<void> {
    let id = window.localStorage.getItem("orderid")
    console.log("id ", id)
    if (id) {
      this.s = await this.fetchOrder(id)
    }
  }

  private async fetchOrder(id:string) {
    let s = (await firstValueFrom(this.odooEM.search<FSMOrder>(new FSMOrder(), [['id', 'ilike', id]])))[0]
    await firstValueFrom(this.odooEM.resolveSingle<FSMLocation>(new FSMLocation(), s.location_id))
    return s
  }

  async onOrder(order:FSMOrder|null) {
    this.isPickingOrder = false
    if (order && order.id) {
      this.s = await this.fetchOrder(order.id.toString())
      window.localStorage.setItem("orderid", order.id.toString()) // temp
    }
  }

  async open() {
    if (!this.s) return
    await firstValueFrom(this.odooEM.update<FSMOrder>(this.s, {"stage_id": FSMOrder.STAGE_ID_OPEN}))
    this.router.navigate(["q/" + this.s.id.toString()])
  }

  cancel() {
    this.s = null
  }

}
