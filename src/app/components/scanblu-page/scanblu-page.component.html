<div class="d-flex flex-column w-100 h-100">
  <app-nav></app-nav>

  <div class="d-flex flex-column align-items-center justify-content-center vh-100 w-80">
    <div class="barcode-input-wrapper text-center w-25">
      <p>
        <span>Scansiona il codice del sacco blu</span>
      </p>
      <div class="d-flex">
        <input
          autofocus
          class="form-control"
          name="barcode"
          #barcodeInput
          (keyup.enter)="onBarcodeScan(barcodeInput.value)"
        />
        <button
          class="btn btn-primary ms-3 scan-check bg-primary border-0"
          (click)="onBarcodeScan(barcodeInput.value)"
        >
          <i class="fa fa-check"></i>
        </button>
      </div>
                  <!-- Optional: Feedback message -->
                  <div class="mt-2 w-100 d-flex flex-column align-items-center justify-content-center">
                    <div *ngIf="barcodeMessage" [ngClass]="{ 'text-primary': isBarcodeValid, 'text-danger': !isBarcodeValid }"
                      style="width: max-content; text-align: center;">
                      {{ barcodeMessage }}
                    </div>
                  </div>
    </div>
  </div>
</div>