import { Component } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-scanblu-page',
  templateUrl: './scanblu-page.component.html',
})
export class ScanBluPageComponent {
  barcodeMessage = '';
  isBarcodeValid = false;

  constructor(private router: Router) {}

  onBarcodeScan(barcodeValue: string) {
    const trimmedBarcode = barcodeValue.trim();
    if (trimmedBarcode) {
      // You can do validation or async checks here if needed
      this.isBarcodeValid = true;
      this.barcodeMessage = 'Codice valido. Reindirizzamento...';
      setTimeout(() => {
        this.router.navigate(['/scarto-detail'], {
          queryParams: { barcode: trimmedBarcode },
        });
      }, 500); // slight delay to show feedback
    } else {
      this.isBarcodeValid = false;
      this.barcodeMessage = 'Codice non valido. Riprova.';
    }
  }
}
