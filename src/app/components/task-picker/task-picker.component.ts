import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { OperatorFunction, Observable, debounceTime, distinctUntilChanged, map, firstValueFrom } from 'rxjs';
import { MrpProduction } from 'src/app/models/mrp.production.model';
import { OdooEntityManager } from 'src/app/shared/services/odoo-entity-manager.service';

@Component({
  selector: 'app-task-picker',
  templateUrl: './task-picker.component.html'
})
export class TaskPickerComponent implements OnInit {

  public names: Array<{"name": string , "id": number}> = [];
  public model

  @Output() onTask:EventEmitter<number> = new EventEmitter<number>()

  constructor(public odooEM:OdooEntityManager) { }

  async ngOnInit(): Promise<void> {
    var mrpArray = await firstValueFrom(this.odooEM.search<MrpProduction>(new MrpProduction()))
    for (let index = 0; index < mrpArray.length; index++) {
      const m = mrpArray[index];
      this.names.push({"name": m.name,"id": m.id})
    }
  }

  search: OperatorFunction<string, readonly {"name": string , "id": number}[]> = (text$: Observable<string>) =>
    text$.pipe(
      debounceTime(200),
      distinctUntilChanged(),
      map(term => term.length < 2 ? []
        : this.names.filter(v => v.name.toLowerCase().indexOf(term.toLowerCase()) > -1).slice(0, 10))
    )

  pickTask($event) {
     this.onTask.next( $event.item.id)
  }

  formatter = (x: {name: string}) => x.name;
}
