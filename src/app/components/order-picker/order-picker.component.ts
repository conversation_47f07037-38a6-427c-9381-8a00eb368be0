import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { firstValueFrom } from 'rxjs';
import { FSMOrder } from 'src/app/models/fsm.oder.model';
import { OdooEntityManager } from 'src/app/shared/services/odoo-entity-manager.service';

@Component({
  selector: 'app-order-picker',
  templateUrl: './order-picker.component.html'
})
export class OrderPickerComponent implements OnInit {
  
  @Output() onOrder:EventEmitter<FSMOrder|null> = new EventEmitter<FSMOrder|null>();
  os: FSMOrder[];

  constructor(public odooEM: OdooEntityManager) { }

  async ngOnInit(): Promise<void> {
    this.os = await firstValueFrom(this.odooEM.search<FSMOrder>(new FSMOrder()))
  }


  close() {
    this.onOrder.emit(null)
  }

  onClickOrder(o:FSMOrder) {
    this.onOrder.emit(o)
  }
}
