import { AfterViewInit, Component, Input, OnInit } from '@angular/core';
import { MrpWorkcenter } from 'src/app/models/mrp.workcenter';

@Component({
  selector: 'app-work-order-state',
  templateUrl: './work-order-state.component.html',
})
export class WorkOrderStateComponent implements OnInit {
  
  @Input() workOrders
  @Input() mprId
  href: string;

  constructor() { }

  async ngOnInit(): Promise<void> {

    for (let index = 0; index < this.workOrders.length; index++) {
      const w = this.workOrders[index];
      // if(!w) continue;

      if(w.workcenter_id.id== 1) // taratura
        w.href = "pesatura_tara/"
      else if(w.workcenter_id.id == 2)
        w.href = "pesatura_pre_camera/"
      else if(w.workcenter_id.id == 4)
        w.href = "pesatura_post_camera/"
      else if(w.workcenter_id.id == 5)
        w.href = "stagionatura/"
      else if(w.workcenter_id.id == 6)
        w.href = "taglio/"


        if(w.id == 107)
        console.log( w )
    }
  }
  
}
