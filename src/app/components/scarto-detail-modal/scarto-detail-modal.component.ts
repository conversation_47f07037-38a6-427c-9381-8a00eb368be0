import { Component, EventEmitter, Output } from '@angular/core';

@Component({
  selector: 'app-scarto-detail-modal',
  templateUrl: './scarto-detail-modal.component.html',
  styleUrls: ['./scarto-detail-modal.component.css']
})
export class ScartoDetailModalComponent {
  kgValue: string = '';

  @Output() confirmKg = new EventEmitter<number>();

  constructor() {}

  appendNumber(num: string) {
    if (this.kgValue.length < 10) {
      this.kgValue += num;
    }
  }

  clearInput() {
    this.kgValue = '';
  }

  onConfirm() {
    const kg = parseInt(this.kgValue, 10);
    if (!isNaN(kg)) {
      this.confirmKg.emit(kg);
    }
  }
}