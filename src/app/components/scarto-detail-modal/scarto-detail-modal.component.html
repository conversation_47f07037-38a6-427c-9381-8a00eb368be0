<!-- <div class="modal-header justify-content-end">
  <button type="button" class="close" (click)="activeModal.dismiss()">
    <span>&times;</span>
  </button>
</div> -->

<div class="modal-body text-center">
  <label for="kgInput">Selected Kg:</label>
  <div class="form-control w-50 mx-auto text-center value">{{ kgValue || '0' }}</div>

  <!-- Number Selection Keypad -->
  <div class="d-flex flex-wrap justify-content-center mt-3" style="max-width: 380px; margin: auto;">
    <!-- Numbers 1-9 -->
    <button *ngFor="let num of ['1','2','3','4','5','6','7','8','9']" 
            class="number btn btn-primary m-1"
            (click)="appendNumber(num)">
      {{ num }}
    </button>

    <!-- Decimal Point -->
    <button class="btn btn-primary number m-1"  (click)="appendNumber('.')">.</button>

    <!-- Number 0 -->
    <button class="btn btn-primary number m-1" (click)="appendNumber('0')">0</button>

    <!-- Clear Button -->
    <button class="btn number m-1" (click)="clearInput()">
      <i class="fa fa-caret-square-o-left" aria-hidden="true"></i>
    </button>
  </div>
</div>

<div class="modal-footer" style="width: 100%; justify-content: center; padding: 0;">
  <button class="btn btn-primary w-100 py-3" (click)="onConfirm()" [disabled]="kgValue === ''">
    Pesa
  </button>
</div>