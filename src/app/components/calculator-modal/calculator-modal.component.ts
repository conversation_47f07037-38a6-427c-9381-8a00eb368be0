import { Component, EventEmitter, Output } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-calculator-modal',
  templateUrl: './calculator-modal.component.html',
  styleUrls: ['./calculator-modal.component.css']
})
export class CalculatorModalComponent {
  value: string = '';

  @Output() confirmValue = new EventEmitter<number>();

  constructor(public activeModal: NgbActiveModal) {}

  appendNumber(num: string) {
    if (this.value.length < 10) {
      this.value += num;
    }
  }

  clearInput() {
    this.value = '';
  }

  onConfirm() {
    const value = parseInt(this.value, 10);
    if (!isNaN(value)) {
      this.confirmValue.emit(value);
      this.activeModal.close();
    }
  }
}