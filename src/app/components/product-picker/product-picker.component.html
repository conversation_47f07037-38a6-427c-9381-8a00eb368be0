<nav *ngIf="!embedded" class="navbar bg-primary navbar-expand-lg navbar-dark shadow">
    <form class="d-flex w-100">
        <button class="btn btn-outline-white me-2" type="button" (click)="onClose()">
            <i class="fa fa-arrow-left text-white"></i>
        </button>
        <span class="navbar-brand">Carica / Scarica</span>
        <!-- <input class="form-control me-2 bg-primary text-white w-100" type="search" placeholder="Cerca" aria-label="Search"> -->
    </form>
</nav>


<div class="list-group list-group-flush pt-4">
    <a *ngFor="let p of result"  class="list-group-item list-group-item-action bg-transparent d-flex align-items-center" >
        <span class="">{{p.product.name}}</span>
        <span class="me-auto text-muted">&nbsp;(Pz)</span>
        <button class="btn bg-primary rounded-circle text-white me-2" (click)="p.qty = p.qty - 1">
            <i class="fa fa-minus"></i>
        </button>
        
        <input class="form-control" style="width:3em" value="0" [(ngModel)]="p.qty">
        
        <button class="btn bg-primary rounded-circle text-white ms-2" (click)="p.qty = p.qty + 1">
            <i class="fa fa-plus"></i>
        </button>
    </a>
</div>


<button class="btn btn-primary btn-largen mx-auto mt-5 d-block " (click)="pickProducts()">Conferma</button>

