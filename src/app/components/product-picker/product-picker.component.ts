import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { firstValueFrom } from 'rxjs';
import { ProductProduct } from 'src/app/models/product.product';
import { OdooEntityManager } from 'src/app/shared/services/odoo-entity-manager.service';



export class ProductPickerResult {
  product:ProductProduct
  qty:number
}

@Component({
  selector: 'app-product-picker',
  templateUrl: './product-picker.component.html',
  styleUrls: ['./product-picker.component.scss']
})
export class ProductPickerComponent implements OnInit {

  result:ProductPickerResult[] = []
  ps:ProductProduct[] = []
  @Output() onProduct:EventEmitter<ProductPickerResult[]> = new EventEmitter<ProductPickerResult[]>()
  @Input() embedded:boolean

  constructor(public odooEm:OdooEntityManager) { }

  async ngOnInit(): Promise<void> {
    this.ps = await firstValueFrom(this.odooEm.search<ProductProduct>(new ProductProduct()))
    this.ps.forEach(p => {
      var r = new ProductPickerResult()
      r.product = p
      r.qty = 0
      this.result.push(r)
    })
  }

  pickProducts() {
    this.onProduct.emit(this.result)
  }
  
  onClose() {
    this.onProduct.emit(this.result)
  }
}
