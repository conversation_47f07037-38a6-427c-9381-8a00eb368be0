<div class="modal" tabindex="-1" id="modalCalibration">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Peso tara</h5>
        <button
          type="button"
          class="btn-close"
          (click)=" manualCalibration = false;  "
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>
      <div class="modal-body" >

<ng-container  *ngIf="!manualCalibration" >
  <p>POSIZIONA BANCALE <b>{{mrpProduction.name}} - {{activePallet.num}} </b> ..</p>
</ng-container>


      <ng-container *ngIf="manualCalibration">


        <input
        [(ngModel)]="defaultValue"
        type="number"
        step="0.01"
        (keyup.enter)="saveValue()"
        class="text-center form-control-lg mb-2 w-100"
        id="taraInput"
      />
        <table class="table">

          <tbody>
            <tr>
              <td scope="row"  (click)="pressNumeric(1)" class="text-center">1</td>
              <td (click)="pressNumeric(2)" class="text-center">2</td>
              <td (click)="pressNumeric(3)" class="text-center">3</td>
            </tr>
            <tr>
              <td (click)="pressNumeric(4)" class="text-center" scope="row">4</td>
              <td (click)="pressNumeric(5)" class="text-center">5</td>
              <td (click)="pressNumeric(6)" class="text-center">6</td>
            </tr>
            <tr>
              <td (click)="pressNumeric(7)" class="text-center" scope="row">7</td>
              <td (click)="pressNumeric(8)" class="text-center">8</td>
              <td (click)="pressNumeric(9)" class="text-center">9</td>
            </tr>
            <tr>
              <td (click)="pressNumeric(',')" class="text-center" scope="row">,</td>
              <td (click)="pressNumeric(0)" class="text-center"> 0</td>
              <td (click)="pressNumeric('cancel')" class="text-center"> < </td>
            </tr>
          </tbody>
        </table>

      </ng-container>
      

        <div class="d-grid gap-2">

          <button
            type="button"
            (click)="setManualCalibration() "
            *ngIf="manualCalibration  == false"
            class="btn btn-success text-white"
          >
            {{label}} Manuale
          </button>
          <!--todo attaccare bilancia-->
          <button
            type="button"
            (click)="saveValue()"
            *ngIf="manualCalibration ==  true"
            class="btn btn-success text-white"
          >
            Salva
          </button>
        </div>
      </div>
   
    </div>
  </div>
</div>
