import { AfterViewInit, Component, EventEmitter, Input, Output } from '@angular/core';
import { MrpProduction } from 'src/app/models/mrp.production.model';
import { Pallet } from 'src/app/models/pallet.model';

@Component({
  selector: 'app-calibration-modal',
  templateUrl: './calibration.modal.html',
})
export class CalibrationPalletModalComponent implements AfterViewInit {

  constructor() { }
  
  @Input() defaultValue

  manualCalibration = false
  @Input() activePallet : Pallet
  @Input() mrpProduction : MrpProduction
  @Input() label : string
  @Output() onCalibration:EventEmitter<Number> = new EventEmitter<Number>()

  ngAfterViewInit(): void {
    //Called after ngAfterContentInit when the component's view has been initialized. Applies to components only.
    //Add 'implements AfterViewInit' to the class.

    if(this.defaultValue == "0")
      this.defaultValue = ""
  }

  setManualCalibration(){
    this.manualCalibration = true; 

    setTimeout(() => {
      document.getElementById("taraInput")?.focus()
    }, 1);

    if(this.defaultValue == '0')
      this.defaultValue = ""
  }


  pressNumeric(v){


    if(!this.defaultValue) this.defaultValue = ""

    this.defaultValue = this.defaultValue.toString()
    
    if(v == "cancel"){
      this.defaultValue = this.defaultValue.slice(0, -1)
      var char = this.defaultValue.charAt(this.defaultValue.length - 1);
      if( char == ".") // . from html always
        this.defaultValue = this.defaultValue.slice(0, -1)
    }
    else if(v == ","){
      this.defaultValue   = this.defaultValue + '.01'
    }
    else
      this.defaultValue   = this.defaultValue + v
    document.getElementById("taraInput")?.focus()
  }

  async saveValue(){
    // se il modalità manuale, trasformerà la stringa to number
    var value;
    if( this.manualCalibration){
      value = Number( this.defaultValue )
    }
    this.onCalibration.next(value)
    this.manualCalibration = false
    this.defaultValue = ""


    
  }
}