import { AfterContentInit, AfterViewInit, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
@Component({
  selector: 'app-input-modal',
  templateUrl: './input.modal.html',
})
export class InputModal implements OnInit {
  @Input() defaultValue: any = 0; // Initialize to null or undefined
  @Input() title: string;
  @Input() modal_id: string;
  @Input() type: string;
  @Output() onChange: EventEmitter<number> = new EventEmitter<number>();
  @Output() onChangeForced: EventEmitter<number> = new EventEmitter<number>();

  warningQty: boolean = false;
  restoreValue: any;
  available_qty: any;

  // Local variable to hold the current input value
  currentValue: any;

  constructor() {}

  ngOnInit(): void {
    console.log("INIT INPUT MODAL");
    
    // Set the local value to defaultValue, if provided, otherwise leave it empty
    this.currentValue = this.defaultValue || '';
  }

  restore() {
    this.warningQty = false;
    this.currentValue = this.restoreValue; // Restore the value when needed
    this.onChange.next(this.currentValue);
  }

  showWarningQty(restoreValue: any, available_qty: any) {
    this.warningQty = true;
    this.restoreValue = restoreValue;
    this.available_qty = available_qty;
  }

  submit(forcing?: boolean) {
    this.warningQty = false;

    // Emit the current value and then clear it

    console.log("SUBMIT INPUT MODAL", this.currentValue);
    if (forcing) {
      this.onChangeForced.next(this.currentValue);
    } else {
      this.onChange.next(this.currentValue);
    }

    // Clear the input field after submission
    this.currentValue = ''; // Clear the input
  }
}
