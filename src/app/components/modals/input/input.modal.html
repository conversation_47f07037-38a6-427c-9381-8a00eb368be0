<div class="modal" tabindex="-1" id="{{modal_id}}">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">{{title}}</h5>
          <button
            type="button"
            class="btn-close"
            data-bs-dismiss="modal"
            aria-label="Close"
          ></button>
        </div>
  
        <div class="modal-body">

              <form class="form">
                    <div class="mb-3">
                      <input id="input"  (keyup.enter)="submit()" type="number" min="0" class="form-control" name="i" [(ngModel)]="currentValue">
                      <!-- <input id="input" *ngIf="type == 'text'" (keyup.enter)="submit()" type="text" class="form-control" name="i" [(ngModel)]="currentValue">                       -->
                    </div>

                    
                    <div class="d-flex my-4">
                      <button class="btn btn-primary me-2 py-3 w-100" (click)="currentValue = currentValue + 0.5">1/2</button>
                      <button class="btn btn-primary me-2 py-3 w-100" (click)="currentValue = currentValue + 0.25">1/4</button>
                      <button class="btn btn-primary me-2 py-3 w-100" (click)="currentValue = currentValue + 0.125">1/8</button>
                      <button class="btn btn-primary w-100 py-3" (click)="currentValue = currentValue + 0.0625">1/16</button>
                    </div>
                  
              </form>

              <div class="d-grid gap-1">
                <button (click)="submit()" [hidden]="warningQty" class="btn text-white btn-success">Conferma</button>
              
              

                <div  [hidden]="!warningQty" class="alert alert-danger" role="alert">
                  Attenzione - Quantità supera la disponibilità  ({{available_qty}})
                </div>
                <button [hidden]="!warningQty"  (click)="submit(true)"  class="btn text-white btn-danger">Conferma </button>
                <button [hidden]="!warningQty" (click)="restore()" class="btn text-white btn-success">Annulla</button>



              </div>
        </div>
  
  
      </div>
    </div>
  </div>
  