import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import * as moment from 'moment';
import { firstValueFrom } from 'rxjs';
import { MrpProduction } from 'src/app/models/mrp.production.model';
import { Mrp<PERSON><PERSON>korder } from 'src/app/models/mrp.workorder';
import { OdooEntityManager } from 'src/app/shared/services/odoo-entity-manager.service';
@Component({
  selector: 'app-mrp-production-modal',
  templateUrl: './mrp-production.modal.html',
})
export class MrpProductionModal implements OnInit {

  constructor(public odooEM: OdooEntityManager) { }
  arrayMrpProduction: Array<MrpProduction> = []
  @Output() onSelect:EventEmitter<MrpProduction> = new EventEmitter<MrpProduction>()

  async ngOnInit(): Promise<void> {
    var arrayMrp = await firstValueFrom(this.odooEM.search<MrpProduction>(new MrpProduction(), []))
    await firstValueFrom(this.odooEM.resolveArray<MrpWorkorder,MrpProduction>(new MrpWorkorder(), arrayMrp, 'workorder_ids'))
    arrayMrp = arrayMrp.sort((a, b) => (a.date_planned_start > b.date_planned_start) ? -1 : 1)

    console.log("arrayMrp ", arrayMrp.length)
    this.arrayMrpProduction = []
    for (let index = 0; index < arrayMrp.length; index++) {
      const m = arrayMrp[index]

      if( m.workorder_ids.values){

        for (let index2 = 0; index2 < m.workorder_ids.values?.length; index2++) {
          const w = m.workorder_ids.values[index2];
          if( w && w.workcenter_id && w.workcenter_id.id == 5 )
            this.arrayMrpProduction.push( m )
        } 
      }
    }
  }

  select(m : MrpProduction) {
    this.onSelect.next(m)
  }

}
