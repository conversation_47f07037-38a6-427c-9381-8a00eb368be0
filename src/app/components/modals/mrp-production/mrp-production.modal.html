<div class="modal" tabindex="-1" id="modalMrpProduction">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">Scegli lotto di produzione</h5>
          <button
            type="button"
            class="btn-close"
            data-bs-dismiss="modal"
            aria-label="Close"
          ></button>
        </div>
  
        <div class="modal-body">
            <div class="table-responsive">
              <table class="table w-100 align-middle table-hover">
                <thead>
                  <tr>
                    <th class="ps-0" scope="col">Lotto</th>
                    <th class="" scope="col">Descrizione</th>
                  </tr>
                </thead>
                <tbody class="table-group-divider">
                  <tr *ngFor="let m of arrayMrpProduction"  (click)="select(m)" >
                    <th class="ps-0" scope="row">
                      {{m.name}}
                    </th>
                    <td>
                      {{m.product_id.name}}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
 
         
  
        </div>
  
      </div>
    </div>
  </div>
  