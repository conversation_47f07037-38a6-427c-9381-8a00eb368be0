import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { StockQuant } from 'src/app/models/stock.quant.model';

@Component({
  selector: 'app-stock-quant-modal',
  templateUrl: './stock-quant.modal.html'
})
export class StockQuantModal implements OnInit {

  constructor() { }
  @Input() title
  @Input() stockQuantArray : StockQuant[]
  @Input() modal_id
  @Output() onChoosed:EventEmitter<StockQuant> = new EventEmitter<StockQuant>()

  ngOnInit(): void {
  }

  select( choose : StockQuant) {
    this.onChoosed.next(choose)
  }
}

