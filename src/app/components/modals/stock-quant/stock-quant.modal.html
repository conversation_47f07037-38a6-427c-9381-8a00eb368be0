<div class="modal" tabindex="-1" id="{{modal_id}}">
    <div class="modal-dialog modal-lg modal-dialog-scrollable">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">SCEGLI STOCKQUANT</h5>
          <button
            type="button"
            class="btn-close"
            data-bs-dismiss="modal"
            aria-label="Close"
          ></button>
        </div>
  
        <div class="modal-body">
          <div class="table-responsive">
            <table class="table w-100 align-middle">
                <thead>
                  <tr class="text-nowrap" >
                    <th scope="col">Collo</th>
                    <th scope="col" class="ps-3">Numero di Lotto</th>
                    <th scope="col">Quantità</th>
                  </tr>
                </thead>
                <tbody class="">
                  <tr *ngFor="let s of stockQuantArray" (click)="select(s)" >

                    <th scope="row" class="ps-3"> {{s.package_id.name}}</th>
                    <td>{{s.lot_id.name}}</td>
                    <td>{{s.available_quantity}}</td>
                  </tr>
                </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
  