<div class="modal" tabindex="-1" id="modalAging" >
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">STAGIONATURA</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>

        <div class="modal-body" *ngIf="pallet && !pallet.start_ageing " > 

          <p>INIZIARE LA STAGIONATURA PER LOTTO N.: {{mrpProduction.name}} - {{pallet.num}}.</p>
        </div>

        <div class="modal-body" *ngIf="pallet && pallet.start_ageing && !pallet.end_ageing " >
            <p>TERMINARE LA STAGIONATURA PER LOTTO N.: {{mrpProduction.name}}  - {{pallet.num}}.</p>
        </div>


        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
          <button type="button" *ngIf="pallet && !pallet.start_ageing " (click)="startTimer()" class="btn btn-primary" >OK</button>
          <button type="button" *ngIf="pallet && pallet.start_ageing && !pallet.end_ageing " (click)="stopTimer()" class="btn btn-danger" >STOP</button>
        </div>
      </div>
    </div>
  </div>