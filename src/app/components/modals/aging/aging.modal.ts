import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { MrpProduction } from 'src/app/models/mrp.production.model';
import { Pallet } from 'src/app/models/pallet.model';
@Component({
  selector: 'app-aging-modal',
  templateUrl: './aging.modal.html',
})
export class AgingModalComponent implements OnInit {
  startAging: number;
  completeAging: number;

  constructor() { }
  status = ""  // start, run , completed

  @Input() pallet : Pallet
  @Input() mrpProduction : MrpProduction
  @Output() onStart:EventEmitter<Date> = new EventEmitter<Date>()
  @Output() onEnd:EventEmitter<Date> = new EventEmitter<Date>()

  ngOnInit(): void {}

  _setStatus( status: string){
    this.status = status
  }

  startTimer(){
    this._setStatus("run")
    // this.pallet["start_ageing"] = new Date()
    this.onStart.next(new Date())

  }

  stopTimer(){
    this._setStatus("stop")
    // this.pallet["end_ageing"] = new Date()
    this.onEnd.next(new Date())
  }

}
