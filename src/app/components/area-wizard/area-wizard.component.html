
<nav class="navbar bg-primary navbar-expand-lg navbar-dark shadow">
    <form class="d-flex w-100">
        <button class="btn btn-outline-white me-2" type="button" (click)="cancel()">
            <i class="fa fa-arrow-left text-white"></i>
        </button>
        <input class="form-control me-2 bg-primary text-white w-100" type="search" placeholder="Cerca" aria-label="Search">
    </form>
</nav>


<div class="card m-3">
    <div class="card-body p-0">
        <div class="list-group list-group-flush">
            <ng-container *ngFor="let e of equipments">
                <a  class="list-group-item list-group-item-action bg-transparent"
                    (click)="selectedEquipment = e">
                    {{e.name}}
                </a>
            </ng-container>
        </div>        
    </div>
</div>


<div class="button-overlay p-4" *ngIf="selectedEquipment" (click)="selectedEquipment = null">
    <div class="d-grid gap-2">
        <button class="btn btn-primary" type="button" (click)="replace()">Presente</button>
        <button class="btn btn-primary" type="button" (click)="replace()">Non presente</button>
        <button class="btn btn-primary" type="button" (click)="replace()">Presente non censita</button>
      </div>
</div>


<div *ngIf="showNFC" class="nfc d-flex justify-content-center align-items-center" (click)="showNFC = false;showNFCSuccess=true;">
    <i class="fa-solid fa-nfc fa-4x"></i>
</div>


<div *ngIf="showNFCSuccess" class="nfc d-flex justify-content-center d-flex flex-column px-3" >
    <i class="mx-auto fa-solid fa-check fa-4x text-success"></i>
    <br>
    <h2 class="text-center">Associazione avvenuta</h2>
    

    <br><br>
 
    <app-product-picker (onProduct)="onProduct($event)" [embedded]="true"></app-product-picker>

</div>