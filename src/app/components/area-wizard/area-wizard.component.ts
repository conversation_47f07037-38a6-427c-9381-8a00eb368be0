import { ThisR<PERSON>eiver } from '@angular/compiler';
import { AfterViewInit, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { firstValueFrom } from 'rxjs';
import { FSMEquipment } from 'src/app/models/fsm.equipment';
import { FSMLocation } from 'src/app/models/fsm.location';
import { FSMVehicle } from 'src/app/models/fsm.vehicle.model';
import { StockMove } from 'src/app/models/stock.move';
import { StockPicking } from 'src/app/models/stock.picking';
import { OdooEntityManager } from 'src/app/shared/services/odoo-entity-manager.service';
import { ProductPickerResult } from '../product-picker/product-picker.component';

@Component({
  selector: 'app-area-wizard',
  templateUrl: './area-wizard.component.html',
  styleUrls: ['./area-wizard.component.scss']
})
export class AreaWizardComponent implements OnInit, AfterViewInit {

  @Output() onDone:EventEmitter<void> = new EventEmitter()
  @Input() location:FSMLocation 
  equipments: FSMEquipment[] = [];
  selectedEquipment:FSMEquipment|null;
  showNFC:boolean = false;
  showNFCSuccess:boolean = false;
  isPickingProduct:boolean = false;
  v: FSMVehicle;

  constructor(public odooEM:OdooEntityManager) { }

  async ngAfterViewInit(): Promise<void> {

    // todo replace with proper model
    let id = window.localStorage.getItem("vehicleid")
    console.log("idx ", id)
    if (id) {
      this.v = (await firstValueFrom(this.odooEM.search<FSMVehicle>(new FSMVehicle(), [['id', 'ilike', id]])))[0]
    }

    this.equipments = await firstValueFrom(
      this.odooEM.search<FSMEquipment>(new FSMEquipment(),[['current_location_id',"=", this.location.id]])
    )
  }

  ngOnInit(): void {
    
  }

  replace() {
    this.showNFC = true
  }

  async onProduct(res:ProductPickerResult[]) {
    var x = await firstValueFrom(this.odooEM.call(
      new StockPicking(), 
      "create", 
      null,
      null,
      {
        "location_id": this.v.inventory_location_id.id,
        "location_dest_id" : 5,
        "picking_type_id": 35
      }));
    
    console.log("XXX", x)
    

    for (let z = 0; z < res.length; z++ ) {
      
      let r = res[z]
      
      if (r.qty > 0) {
        var xx = await firstValueFrom(this.odooEM.call(
          new StockMove(), 
          "create", 
          null,
          null,
          {
            "name":"",
            "picking_id": x[0],
            "location_id": this.v.inventory_location_id.id,
            "location_dest_id" : 5,
            "product_id": r.product.id,
            "product_uom": 1,
            "product_uom_qty": r.qty,
            "quantity_done": r.qty
          }));
      }
    }

    await firstValueFrom(this.odooEM.call(
      new StockPicking(),
      "action_confirm",
      null,
      null,
      x[0]
    ))

    await firstValueFrom(this.odooEM.call(
      new StockPicking(),
      "button_validate",
      null,
      null,
      x[0]
    ))

    this.onDone.emit()
  }

  cancel() {
    
    this.onDone.emit()
  }

}
