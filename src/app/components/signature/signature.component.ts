import { Component, EventEmitter, OnInit, Output } from '@angular/core';

@Component({
  selector: 'app-signature',
  templateUrl: './signature.component.html',
  styleUrls: ['./signature.component.scss']
})
export class SignatureComponent implements OnInit {

  @Output() onSignature:EventEmitter<null> = new EventEmitter<null>()

  constructor() { }

  ngOnInit(): void {

  }


  onCancel() {
    this.onSignature.emit(null)
  }
  
  onOk() {
    this.onSignature.emit(null)
  }
}
