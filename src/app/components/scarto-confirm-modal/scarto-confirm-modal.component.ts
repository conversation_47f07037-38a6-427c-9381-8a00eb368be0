import { Component, EventEmitter, Output } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { Router } from '@angular/router';

@Component({
  selector: 'app-scarto-confirm-modal',
  templateUrl: './scarto-confirm-modal.component.html',
  styleUrls: ['./scarto-confirm-modal.component.css']
})
export class ScartoConfirmModalComponent {
  kgValue: string = '';

  @Output() confirm = new EventEmitter<void>();

  
  constructor(private router: Router, public activeModal: NgbActiveModal) {}

  onConfirm() {
    this.confirm.emit();
    this.closeModal();
    this.router.navigate(['/production']);
  }

  onCancel() {
    this.activeModal.close()
    window.history.back(); // Navigate back
  }

  closeModal() {
    this.activeModal.close();
  }
}