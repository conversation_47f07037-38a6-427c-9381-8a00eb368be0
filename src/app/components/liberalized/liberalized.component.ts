import { DatePipe } from '@angular/common';
import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { firstValueFrom } from 'rxjs';
import { CONFIG } from 'src/app/CONFIG';
import { MrpProduction } from 'src/app/models/mrp.production.model';
import { ProductProduct } from 'src/app/models/product.product';
import { ProductTemplateAttributeValue } from 'src/app/models/product.template.attribute.value.model';
import { StockPicking } from 'src/app/models/stock.picking';
import { StockProductionLot } from 'src/app/models/stock.production-lot';
import { StockQuant } from 'src/app/models/stock.quant.model';
import { StockQuantPackage } from 'src/app/models/stock.quant.package';
import { OdooEntityManager } from 'src/app/shared/services/odoo-entity-manager.service';

@Component({
  selector: 'app-liberalized',
  templateUrl: './liberalized.component.html',
  styleUrls: ['./liberalized.component.scss']
})
export class LiberalizedComponent implements OnInit {

  packages: StockQuantPackage[];
  package_meta = {}

  constructor(
    private odooEM:OdooEntityManager,
    private ref: ChangeDetectorRef,
    public datepipe: DatePipe
  ) { }

  async ngOnInit(): Promise<void> {

    let packages = await firstValueFrom(this.odooEM.search<StockQuantPackage>(new StockQuantPackage(), [['location_id', '=', 93]]))
    await firstValueFrom(this.odooEM.resolveArray(new StockQuant(),packages,"quant_ids"))

    for (let stock_package of packages) {


      
      
      if (!stock_package.quant_ids.values) {
         continue
      }

      let stock_quant = stock_package.quant_ids.values[0]
      let lot_id = stock_quant.lot_id
      let mo = (await firstValueFrom(this.odooEM.search<MrpProduction>(new MrpProduction, [
        ['lot_producing_id', '=', lot_id.id],
        ['picking_type_id', '=', 81]
      ])))[0]
      let lot =(await firstValueFrom(this.odooEM.search<StockProductionLot>(new StockProductionLot(), [['id', '=', lot_id.id]])))[0]
      let pickings = await firstValueFrom(this.odooEM.search<StockPicking>(new StockPicking(), [['id', 'in', mo.picking_ids.ids]]))

      // get the starting date of seasoning if any
      var s = pickings.find(p => p.location_dest_id?.id == CONFIG.seasoning_location_id)
      if (s) {
          await firstValueFrom(this.odooEM.resolveSingle<ProductProduct>(new ProductProduct(), stock_quant.product_id))
          let attrs = stock_quant.product_id.value.product_template_attribute_value_ids
          await firstValueFrom(this.odooEM.resolve<ProductTemplateAttributeValue>(attrs))
          let min = attrs.values?.find(a => a.product_attribute_value_id.name.includes('minim'))?.name
          let max = attrs.values?.find(a => a.product_attribute_value_id.name.includes('massim'))?.name

          var agingDate = new Date(s.date_done)
          var today = new Date();
          var diff = Math.abs(agingDate.getTime() - today.getTime());
          var diffDays = Math.ceil(diff / (1000 * 3600 * 24)); 

          this.package_meta[stock_package.name] = {
            'min': min,
            'max': max,
            'aging': this.datepipe.transform(new Date(s.date_done), 'dd/MM/yyyy'),
            'aging_diff': diffDays,
            'mo_date': this.datepipe.transform(new Date(mo.date_planned_start), 'dd/MM/yyyy'),
            'lot': lot
          }

      }

    }

  

    this.packages = packages

  }

  metaForPackage(packageName, attribute): String {

    let packageMeta = this.package_meta[packageName]

    if (packageMeta === undefined || packageMeta[attribute] === undefined) {
      return ""
    }

    return packageMeta[attribute]
  }



}
