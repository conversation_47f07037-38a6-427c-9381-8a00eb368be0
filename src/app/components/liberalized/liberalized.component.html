<div class="d-flex flex-column w-100 h-100">
<app-nav ></app-nav>

<div class="container-fluid d-flex flex-column">

<div class="table-responsive my-5">
    <table class="table w-100 ">
        <thead>
            <tr class="text-nowrap" >
                <th class=""><PERSON>lo</th>
                <th class="">Lotto</th>
                <th class="">Prodotto</th>
                <th class="">Disponibilità</th>
                <th class="">Produzione</th>
                <th class="">Stagionatura</th>
                <th class="">Esito</th>
            </tr>
        </thead>
        <tbody>
            <tr *ngFor="let p of packages">
                <td>
                    {{p.name}}
                </td>
                <td>
                    {{p.quant_ids.values && p.quant_ids.values.length ? p.quant_ids.values[0].lot_id.name : ''}}
                </td>
                <td>
                    {{p.quant_ids.values && p.quant_ids.values.length ? p.quant_ids.values[0].product_id.name : ''}}
                </td>
                <td>
                    {{ p.quant_ids.values && p.quant_ids.values.length ? p.quant_ids.values[0].quantity : '' }}
                </td>
                <td>
                    {{ metaForPackage(p.name, 'lot')['production_date'] }}
                </td>
                <td>
                    {{ metaForPackage(p.name, 'lot')['aging_days'] }}
                </td>
                <td>
                    <span class="badge bg-primary" *ngIf="metaForPackage(p.name, 'lot')['state'] == 'favorite'">Preferito</span>
                    <span class="badge bg-success" *ngIf="metaForPackage(p.name, 'lot')['state'] == 'bti'">Banco Taglio Italia</span>
                    <span class="badge bg-success" *ngIf="metaForPackage(p.name, 'lot')['state'] == 'ok'">SI</span>
                    <span class="badge bg-danger" *ngIf="metaForPackage(p.name, 'lot')['state'] == 'ko'">NO</span>
                </td>
            </tr>
        </tbody>
    </table>
</div>
</div>
</div>