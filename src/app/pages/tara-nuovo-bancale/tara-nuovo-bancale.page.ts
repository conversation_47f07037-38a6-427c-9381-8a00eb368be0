import { Component, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { firstValueFrom } from 'rxjs';
import { MrpProduction } from 'src/app/models/mrp.production.model';
import { StockMoveLine } from 'src/app/models/stock.move.line';
import { StockQuantPackage } from 'src/app/models/stock.quant.package';
import { OdooEntityManager } from 'src/app/shared/services/odoo-entity-manager.service';
import { ToastService } from 'src/app/toast/toast.service';
import { CONFIG } from 'src/app/CONFIG';
import { StockMove } from 'src/app/models/stock.move';

import { Pipe, PipeTransform } from '@angular/core';

//sortByCreateDate
@Pipe({
    name: 'sortByCreateDate'
  })
  export class SortByCreateDatePipe implements PipeTransform {
    transform(value: any[]): any[] {
      return value.sort((a, b) => {
        const dateA = new Date(a.create_date).getTime();
        const dateB = new Date(b.create_date).getTime();
        return dateB - dateA; // Ascending order
      });
    }
  }

@Component({
    selector: 'app-tara-nuovo-bancale',
    templateUrl: './tara-nuovo-bancale.page.html',
})
export class TanaNuovoBancalePage {
    loading: boolean = false;
    barcode: string | null = null;  // BA/PR2/00597
    production: MrpProduction | null = null;
    selectedValue: number = 0;
    peso: number | null = null;
    itemList: { id: number; name: string; weighted: boolean }[] = [];
    stockQuantPackages: StockQuantPackage[] = [];
    selectedStockQuantPackage: StockQuantPackage | null = null;
    packagesToCreate: number = 0;

    @ViewChild('taraModal') taraModal;
    isWeighing: boolean;
    quantCreating: boolean;
    packagesToLabel: StockQuantPackage[];

    constructor(private router: Router, private modalService: NgbModal, private activatedRoute: ActivatedRoute, private toastr: ToastService, private odooEM: OdooEntityManager) { }

    getBarcode() {
        return this.activatedRoute.snapshot.queryParamMap.get('barcode');
    }

    onBarcodeScan(barcodeValue: string) {
        if (barcodeValue.trim()) {
            this.router.navigate(['/tara-nuovo-bancale'], { queryParams: { barcode: barcodeValue } });
        }
    }


    
    async ngOnInit() {
        // search package witouh history weight

        this.packagesToLabel = await firstValueFrom(this.odooEM.search<StockQuantPackage>(new StockQuantPackage(), [['quant_ids','=', false],['weight_tracking_ids', '=', false]]));
        
        // this.activatedRoute.queryParamMap.subscribe(params => {
        //     const barcode = params.get('barcode');
        //     if (barcode) {
        //         this.barcode = barcode;
        //         this.load(barcode);
        //         console.log("Scanned barcode:", barcode);
        //     }
        // });
    }


    async onNewPackage() {
        for (let i = 0; i < this.packagesToCreate; i++) {
            var p =await firstValueFrom(this.odooEM.create(new StockQuantPackage(), { 
            }))
            console.log("Created package: ", p)
        }
    }

    async load(barcode: string) {
        
        this.loading = true;
        try {
            const productions = await firstValueFrom(this.odooEM.search<MrpProduction>(new MrpProduction(), [
                ['name', '=', barcode],
            ]))

            if (!productions.length) {
                console.log("No production for name: ", barcode);
                return;
            }

            this.production = productions?.[0] || null;

            console.log("Production: ", this.production);

            await firstValueFrom(this.odooEM.resolve(productions[0]?.move_finished_ids));

            if (productions[0]?.move_finished_ids?.values?.length) {
                await firstValueFrom(this.odooEM.resolveArray(new StockMoveLine(), productions[0]?.move_finished_ids.values, "move_line_ids"))
            }

            const packageIds = this.production?.move_finished_ids?.values?.[0]?.move_line_ids?.values?.map(ml => ml.package_id.id) || [];
            const stockQuantPackages = await firstValueFrom(this.odooEM.search<StockQuantPackage>(new StockQuantPackage(), [
                ['id', 'in', packageIds],
            ]));
            this.stockQuantPackages = stockQuantPackages;

            console.log("PackageIds: ", packageIds)
            console.log("StockQuantPackages: ", this.stockQuantPackages);
        } catch (error) {
            console.error(error);
        } finally {
            this.loading = false;
        }
    }

    async confermaPeso() {
        const count = Number(this.peso);
        if (isNaN(count) || count <= 0) {
            this.toastr.error('Input non valido', 'Errore');
            return;
        }

        this.quantCreating = true;
        try {
            const promises = Array.from({ length: count }, () =>
                this.createSingleStockQuantPackage()
            );

            await Promise.all(promises);
            this.toastr.success('Stampa avvenuta correttamente!', 'Successo');
            this.load(this.barcode!);
        } catch (error) {
            console.error(error);
            this.toastr.error('Errore durante la creazione', 'Errore');
        } finally {
            this.quantCreating = false;
            this.peso = null;
        }
    }

    private async createSingleStockQuantPackage(): Promise<any> {
        const response = (await firstValueFrom(
            this.odooEM.create(new StockQuantPackage(), {
                package_type_id: 36,
            })
        )) as StockQuantPackage;
        
        if (!response) {
            return null;
        }

        console.log("RESPONSE: ", response)


        let moveFinished = this.production?.move_finished_ids?.values?.[0] as StockMove;

        if (!moveFinished) {
            let x = await firstValueFrom(this.odooEM.create<StockMove>(new StockMove(), {
                name: this.production?.product_id.name,
                raw_material_production_id: this.production?.id,
                product_id: this.production?.product_id.id,
                product_uom: this.production?.product_uom_id.id,
                location_id: CONFIG.liberalizzati,
                location_dest_id: CONFIG.liberalizzati
            }))
            if (x) {
                moveFinished = x
            }
        }

        if (moveFinished) {
            await firstValueFrom(this.odooEM.create<StockMoveLine>(new StockMoveLine(), {
              move_id: moveFinished.id,
              product_id: this.production?.product_id.id,
              product_uom_id: this.production?.product_uom_id.id,
            //   lot_id: sq.lot_id.id,
              package_id: response.id,
              location_id: CONFIG.liberalizzati,
              location_dest_id: CONFIG.liberalizzati
            }))
          }
        return response;
    }
}
