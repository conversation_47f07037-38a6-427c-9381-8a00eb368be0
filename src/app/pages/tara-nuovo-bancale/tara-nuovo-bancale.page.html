<div class="d-flex flex-column w-100 h-100">
    <app-nav></app-nav>

    <div class="d-flex justify-content-center align-items-center w-100 h-100" *ngIf="loading">
        <div class="spinner-border" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>

    <div class="mt-4" *ngIf="!loading">
        <!-- <div class="d-flex flex-column align-items-center justify-content-center" *ngIf="!barcode"
            style="height: 80vh;">
            <div class="barcode-input-wrapper text-center w-25">
                <p>
                    <span>Digita il codice dell'ordine</span>
                </p>
                <div class="d-flex gap-2">
                    <input class="form-control" type="text" #barcodeInput />
                    <button class="btn btn-primary text-white" (click)="onBarcodeScan(barcodeInput.value)">
                        <i class="fa fa-search"></i>
                    </button>
                </div>
            </div>
        </div> -->

        <div class="d-flex align-items-center justify-content-between flex-wrap gap-4 px-3" >
            <ol class="breadcrumb  ">
                <li class="breadcrumb-item">
                    <a [routerLink]="['/']"><i class="fa fa-home"></i></a>
                </li>

                <li class="breadcrumb-item">
                    <a [routerLink]="['/tara-nuovo-bancale?barcode=' + barcode]">Etichetta nuovo bancali {{barcode}}</a>
                </li>
            </ol>
        </div>

        <div class="mx-auto d-flex gap-4 border-top">
            <!-- Left column: Sidebar -->
            <div class="border-end w-50">
                <!-- <h5 class="mb-3">Ordine: {{ barcode }}</h5> -->
                <span class="p-3  d-block text-nowrap mb-3">Etichette da stampare</span>

                <ul class="list-group list-group-flush " 
                    style="max-height: 75vh; overflow-y: auto;"
                >
                    <li *ngFor="let p of packagesToLabel | sortByCreateDate"
                        class="list-group-item list-group-item-action cursor-pointer"
                        style="padding-block: 1rem; cursor: pointer;">
                        <b>{{ p.name }}</b>
                        <br>
                        <small class="text-muted">Del {{ p.create_date | date }} </small>
                        <br>
                        <small class="text-muted">Da {{ p.create_uid.name }} </small>
                    </li>
                </ul>
            </div>


            <!-- Right column: Card -->
            <div style="width: 75%; height: 80vh; overflow-y: hidden;"
                class="d-flex flex-column justify-content-center">
                <div class="p-5 text-center" style="max-width: 600px; margin-inline: auto;">
                    <h5 class="mb-4">Quanti nuovi bancali da creare ?</h5>
                    <div class="form-group mb-3">
                        <input type="number" class="form-control p-2" id="weightInput" [(ngModel)]="packagesToCreate"
                            placeholder="Inserisci il numero di bancali">
                    </div>
                    <button class="btn btn-success align-self-end" (click)="onNewPackage()" [disabled]="!packagesToCreate">
                        Conferma
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>