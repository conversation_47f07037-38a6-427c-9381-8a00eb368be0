import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { OdoorpcService } from 'src/app/shared/services/odoorpc.service';

@Component({
  selector: 'app-login',
  templateUrl: './login.page.html'
})
export class LoginPage implements OnInit {

  public pwd: string = "admin";
  public user: string;
  loading: boolean;
  error: string = "";

  constructor(
    private rpc: OdoorpcService,
    private router:Router
  ) {}


  ngOnInit(): void {
  }

  async login() {
    this.loading = true
    var l = await this.rpc.login({
      db: "eva",
      password: this.pwd,
      login: this.user})
    
    console.log( "llll ", l)
    if (l.error) {
      this.error = l.error.data.message
      this.loading = false
    }
    else  {
      this.router.navigate(["/"])
    }
  }
}
