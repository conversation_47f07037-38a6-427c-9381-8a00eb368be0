<app-nav></app-nav>


<div class="d-flex flex-column h-100">

  <div class="d-flex align-items-center my-5 mx-4">
    <ol class="breadcrumb m-0 ">
      <li class="breadcrumb-item">
        <a [routerLink]="['/']"><i class="fa fa-home"></i></a>
      </li>
      
       <li class="breadcrumb-item">
           <a [routerLink]="['/warehouse']">Magazzino</a>
       </li>
       <li class="breadcrumb-item">
         <a ><b>{{pageName}}</b></a>
       </li>
       
     </ol> 
   </div>
 
      <table class="table w-100 table-hover table-bordered">
        <thead>
          <tr>
            <th class="ps-3">Lotto</th>
            <th>Tot. Bancali</th> 
            <th>Data</th> 
            <th>Stato</th> 

          </tr>
        </thead>
        <tbody class="">
          <tr *ngFor="let p of pickings" [routerLink]="['/warehouse/picking',p.id]">
            <td class="ps-3">{{p.origin}}</td>
            <td>{{p.move_line_ids_without_package.ids.length }}</td>
            <td>{{p.scheduled_date | date}}</td> 
            <td>{{p.state}}</td> 

          </tr>
        </tbody>
      </table>


</div>



<div id="scanButton" class="d-flex justify-content-end me-4">
  <button (click)="openScanModal()" class="btn btn-primary">
      <i class="fa fa-scanner-gun fa-3x"></i>
  </button>
</div>

<app-scan-package-modal (onBarcode)="onBarcode($event)"></app-scan-package-modal>

