import { AfterViewInit, Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { firstValueFrom } from 'rxjs';
import { StockLocationRoute } from 'src/app/models/stock.location.route.model';
import { StockMove } from 'src/app/models/stock.move';
import { StockMoveLine } from 'src/app/models/stock.move.line';
import { StockPicking } from 'src/app/models/stock.picking';
import { StockPickingType } from 'src/app/models/stock.picking.type';
import { OdooEntityManager } from 'src/app/shared/services/odoo-entity-manager.service';
import Bootstrap from 'bootstrap/dist/js/bootstrap';
import { StockQuantPackage } from 'src/app/models/stock.quant.package';

@Component({
  selector: 'app-picking-browser',
  templateUrl: './picking-browser.component.html',
})
export class PickingBrowserPage implements AfterViewInit {
  pickings: StockPicking[];
  public idRicezione: number = 8
  breadcrumb_config: [string,string][]
  stockLocationRoute: StockLocationRoute;
  headerTitle: string | undefined;
  pageName: string|undefined = ""
  scanModal: any;

  constructor(private router : Router ,public odooEM: OdooEntityManager, public activatedRoute: ActivatedRoute) { }

  ngOnInit(): void {
    this.scanModal = new Bootstrap.Modal(document.getElementById('scanpackagemodal'))

    
  }
  
  async ngAfterViewInit(): Promise<void> {
    var rs = await this.odooEM.search<StockPickingType>(new StockPickingType(), [['id','=', Number(this.activatedRoute.snapshot.params['id'])  ]]).toPromise()
    if (rs && rs.length > 0)
      this.pageName = rs[0].name.split(":")?.pop()

    this.activatedRoute.url.subscribe(async url => {
      var id:number = Number(this.activatedRoute.snapshot.params['id'])
      this.pickings = (await firstValueFrom(this.odooEM.search<StockPicking>(new StockPicking(),[
        ['picking_type_id','=',id],
        ['state','in',['assigned', 'waiting']],
      ]) )).reverse()
      await this.odooEM.resolveArray(new StockMoveLine(),this.pickings, "move_line_ids_without_package").toPromise()
    });
  }

  selectPicking(id) : void{
    this.router.navigate(['/picking/'+id.toString()]);
  }
  
  getLot(p:StockPicking) {
    var v = p.move_line_ids_without_package.values
    if (v?.length) {
      return v[0].lot_id.name
    }
    return
  }


  openScanModal() {
    this.scanModal.show()
  }
  
  async onBarcode(e) {

    this.scanModal.hide()
    
    var p = await this.odooEM.search<StockQuantPackage>(new StockQuantPackage(), [['name','=', e]]).toPromise()
    if (p && p.length > 0) {
      var c = await this.odooEM.search<StockMoveLine>(new StockMoveLine(), [['result_package_id.name','=',e],['state', '=' ,'assigned']]).toPromise()
      if (c && c.length > 0) {
        this.router.navigate(['warehouse','picking',c[0].picking_id.id])
      }
    }
  }
  

}


