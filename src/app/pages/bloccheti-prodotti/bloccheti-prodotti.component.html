<div class="d-flex flex-column w-100">
  <app-nav></app-nav>
  <div class="container-fluid d-flex flex-column flex-grow-1 px-4 py-3">
    <ol class="breadcrumb m-0 py-3">
      <li class="breadcrumb-item">
        <a [routerLink]="['/']"><i class="fa fa-home"></i></a>
      </li>
      <li class="breadcrumb-item">
        <a [routerLink]="['/bloccheti-prodotti']">Gorgonzola e Mascarpone</a>
      </li>
    </ol>

    <!-- Main flex container for both sections -->
    <div class="d-flex flex-row flex-grow-1 gap-4">
      <!-- First Table Section - Componenti -->
      <div class="card flex-grow-2">
        <div class="card-header d-flex align-items-center justify-content-between bg-light">
          <div>
            <ol class="breadcrumb m-0">
              <li class="breadcrumb-item active">Componenti</li>
            </ol>
          </div>
          <div class="d-flex">
            <input class="form-control me-2" placeholder="Cerca..." />
            <button class="btn btn-link text-primary me-2">
              <i class="fa fa-search"></i>
            </button>
            <button class="btn btn-primary" (click)="openScanModal('componenti')">
              <i class="fa fa-scanner-gun"></i>
            </button>
          </div>
        </div>
        <div class="card-body p-0">
          <div class="table-responsive">
            <table class="table table-striped m-0">
              <thead>
                <tr>
                  <th>Prodotto</th>
                  <th>Lotto</th>
                  <th>Utilizzato</th>
                  <th>Collo</th>
                  <th class="text-end">Azioni</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let item of componentiData; let i = index">
                  <td>
                    <select class="form-control" [(ngModel)]="item.product">
                      <option value="">Seleziona prodotto</option>
                      <option *ngFor="let product of products" [value]="product">
                        {{ product }}
                      </option>
                    </select>
                  </td>
                  <td>
                    <input
                      type="number"
                      class="form-control"
                      [(ngModel)]="item.quantity"
                      placeholder="Quantità"
                    />
                  </td>
                  <td>
                    <select class="form-control" [(ngModel)]="item.pallet">
                      <option value="">Seleziona bancale</option>
                      <option *ngFor="let pallet of pallets" [value]="pallet">
                        {{ pallet }}
                      </option>
                    </select>
                  </td>
                  <td>
                    <input
                      type="text"
                      class="form-control"
                      [value]="item.scannedCode || ''"
                      placeholder="Codice scansionato"
                      readonly
                    />
                  </td>
                  <td class="text-end">
                    <button class="btn btn-sm btn-danger" (click)="removeComponentiItem(i)">
                      <i class="fa fa-trash"></i>
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <!-- Second Table Section - Bloccketti prodotti -->
      <div class="card flex-grow-1">
        <div class="card-header d-flex align-items-center justify-content-between bg-light">
          <div>
            <ol class="breadcrumb m-0">
              <li class="breadcrumb-item active">Bloccketti prodotti</li>
            </ol>
          </div>
          <button class="btn btn-success" (click)="addNewRow()">
            <i class="fa fa-plus text-white"></i>
          </button>
        </div>
        <div class="card-body p-0">
          <div class="table-responsive">
            <table class="table table-hover m-0">
              <thead>
                <tr>
                  <th>Prodotto</th>
                  <th>Quantita</th>
                  <th>Bancale</th>
                  <th>Quantita</th>
                  <th>Action</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let row of blocchettiData; let i = index">
                  <td>
                    <select class="form-control" [(ngModel)]="row.product">
                      <option value="">Select Product</option>
                      <option *ngFor="let product of products" [value]="product">
                        {{ product }}
                      </option>
                    </select>
                  </td>
                  <td>                    
                    <input
                      type="number"
                      class="form-control"
                      [(ngModel)]="row.quantity"
                      placeholder="Quantità"
                    />
                  </td>
                  <td>
                    <div *ngIf="row.scannedCode; else scanButton">
                      <input
                        type="text"
                        class="form-control"
                        [value]="row.scannedCode"
                        readonly
                      />
                    </div>
                    <ng-template #scanButton>
                      <button class="btn btn-primary" (click)="openScanModal('blocchetti', i)">
                        <i class="fa fa-scanner-gun"></i>
                      </button>
                    </ng-template>
                  </td>
                  <td>{{ row.quantity || "48" }}</td>
                  <td>
                    <button class="btn btn-danger" (click)="deleteBlocchettiRow(i)">
                      <i class="fa fa-trash"></i>
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
    <div class="card-footer d-flex justify-content-center py-4">
      <button class="btn btn-primary">Conferma</button>
    </div>
  </div>
</div>

<app-scan-package-modal
  #modal
  (onBarcode)="handleScannedCode($event)"
></app-scan-package-modal>