import { Router } from '@angular/router';
import { Component, ViewChild, OnInit } from '@angular/core';
import { ScanPackageModal } from 'src/app/components/modals/scan-package/scan-package.modal';

@Component({
  selector: 'app-bloccheti-prodotti',
  templateUrl: './bloccheti-prodotti.component.html',
  styleUrls: ['./bloccheti-prodotti.component.scss']
})
export class BlocchetiProdottiComponent implements OnInit {
  products: string[] = ['Product A', 'Product B', 'Product C', 'Product D'];
  pallets = ['Bancale 1', 'Bancale 2', 'Bancale 3', 'Bancale 4'];
  
  currentScanContext: { type: string, index?: number } = { type: '' };
  
  @ViewChild('modal') modal: ScanPackageModal;
  
  // First table data (Componenti)
  componentiData: {
    product: string;
    quantity: number | null;
    pallet: string;
    scannedCode: string | null;
  }[] = [];
  
  // Second table data (Bloccketti prodotti)
  blocchettiData: {
    product: string;
    quantity: number | string;
    scannedCode: string | null;
  }[] = [
    {
      product: '',
      quantity: '48',
      scannedCode: null
    }
  ];

  constructor(private router: Router) { }

  ngOnInit(): void {
  }

  openScanModal(type: string, index?: number) {
    this.currentScanContext = { type, index };
    this.modal.show();
  }

  handleScannedCode(code: string) {
    if (this.currentScanContext.type === 'componenti') {
      // Add new row to Componenti table with scanned code
      this.componentiData.unshift({
        product: '',
        quantity: null,
        pallet: '',
        scannedCode: code
      });
    } else if (this.currentScanContext.type === 'blocchetti' && this.currentScanContext.index !== undefined) {
      // Update scanned code in Bloccketti table
      this.blocchettiData[this.currentScanContext.index].scannedCode = code;
    }
  }

  // Componenti table methods
  removeComponentiItem(index: number) {
    this.componentiData.splice(index, 1);
  }

  // Bloccketti table methods
  addNewRow() {
    this.blocchettiData.push({
      product: '',
      quantity: '48',
      scannedCode: null
    });
  }

  deleteBlocchettiRow(index: number) {
    if (this.blocchettiData.length > 0) {
      this.blocchettiData.splice(index, 1);
    }
  }

  onConfirm() {
    this.router.navigate(['/componenti']);
  }
}