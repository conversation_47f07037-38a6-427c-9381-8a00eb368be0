import { Component, OnInit } from "@angular/core";
import { Router } from "@angular/router";
import { BehaviorSubject, firstValueFrom } from "rxjs";
import { MrpProduction } from "src/app/models/mrp.production.model";
import { StockLocationRoute } from "src/app/models/stock.location.route.model";
import { StockRule } from "src/app/models/stock.rule.model";
import { OdooEntityManager } from "src/app/shared/services/odoo-entity-manager.service";
import { StockMove } from "src/app/models/stock.move";
import { SaleOrder } from "src/app/models/sale-order.model";
import { CONFIG } from "src/app/CONFIG";
import * as moment from "moment";
import { ProductProduct } from "src/app/models/product.product";
import { SaleOrderLine } from "src/app/models/sale-order-line.model";

@Component({
  selector: "app-production-browser",
  templateUrl: "./production-browser.page.html",
})
export class ProductionBrowserPage implements OnInit {
  stockLocationRoute: StockLocationRoute;
  relatedSales: SaleOrder[];

  viewDate: string = new Date().toISOString().slice(0, 10);
  allProductions: MrpProduction[] = [];

  constructor(private router: Router, private odooEM: OdooEntityManager) {}

  productions: MrpProduction[] = [];
  breadcrumb_config: [string, string][];

  async ngOnInit(): Promise<void> {
    this.refresh();
  }



  async refresh() {
    let yesterday = moment(this.viewDate)
      .add(-1, "days")
      .set({ hour: 23, minute: 0, second: 0, millisecond: 0 })
      .format("YYYY-MM-DD HH:mm:ss");
    let tomorrow = moment(this.viewDate)
      .set({ hour: 22, minute: 59, second: 0, millisecond: 0 })
      .format("YYYY-MM-DD HH:mm:ss");

    console.log("dta", yesterday);

    var p = await firstValueFrom(
      this.odooEM.search<MrpProduction>(new MrpProduction(), [
        // ['state' , 'in',["confirmed", "progress","to_close"]],
        ["picking_type_id", "=", CONFIG.confezionamento_type_id],
        ["date_planned_start", ">=", yesterday],
        ["date_planned_start", "<=", tomorrow],
        ["state", "!=", "cancel"],
      ])
    );

    // origin relations are weak text fields
    var origins:string[] = []
    p.forEach(pp => {
      if (pp.origin && !origins.includes(pp.origin))
        origins.push(pp.origin)
    })

    this.relatedSales = await firstValueFrom(this.odooEM.search<SaleOrder>(new SaleOrder(), [['name','in', origins]]))

    // await firstValueFrom(this.odooEM.resolveArray(new StockMove() ,p, "move_raw_ids"))
    await firstValueFrom(
      this.odooEM.resolveArrayOfSingle(new ProductProduct(), p, "product_id")
    );
    // await firstValueFrom(
    //   this.odooEM.resolveArrayOfSingle(
    //     new SaleOrderLine(),
    //     p,
    //     "related_sale_order_line"
    //   )
    // );
    // await firstValueFrom(
    //   this.odooEM.resolveArrayOfSingle(new SaleOrder(), p, "related_sale_order")
    // );

    this.allProductions = p;
    this.productions = p.filter((p) => p.product_id?.value?.sale_ok == false);
    
    console.log("Productions", this.productions);
  }


  getStateLabel(p: MrpProduction): string {
    switch (p.state) {
      case "draft":
        return "Bozza";
      case "confirmed":
        return "Confermato";
      case "progress":
        return "In Corso";
      case "done":
        return "Completato";
      case "cancel":
        return "Annullato";
      default:
        return p.state;
    }
  }

  getStateClass(p: MrpProduction): string {
    switch (p.state) {
      case "draft":
        return "bg-danger text-white";
      case "confirmed":
        return "bg-danger text-white";
      case "progress":
        return "bg-warning text-dark";
      case "done":
        return "bg-success text-white";
      case "cancel":
        return "bg-muted text-white";
      default:
        return "";
    }
  }


  getRelatedSale(p: MrpProduction) {
    // if (!p.origin) return null;
    // let l = p.origin.split("-");
    // if (l.length > 0) {
    //   let pp = this.allProductions.find((p) => p.name == l[1].trim());
    //   if (pp) p = pp;
    // }
    return this.relatedSales.find((s) => s.name == p.origin);
  }

  convertToInteger(input: string): string | null {
    const trimmedInput = input.trim();

    const parsedNumber = parseFloat(trimmedInput);
    if (isNaN(parsedNumber)) {
        return "";
    }
    return Math.floor(parsedNumber).toString();
  }
}
