<div class="d-flex flex-column w-100 h-100">
<app-nav></app-nav>

<div class="container-fluid d-flex flex-column">
  <div class="d-flex align-items-center justify-content-between flex-wrap gap-4 my-5">
    <ol class="breadcrumb m-0">
      <li class="breadcrumb-item">
        <a [routerLink]="['/']"><i class="fa fa-home"></i></a>
      </li>

      <li class="breadcrumb-item">
        <a [routerLink]="['/production']">Produzione</a>
      </li>
    </ol>

    <form>
      <input
        name="date"
        class="form-control"
        [(ngModel)]="viewDate"
        (change)="refresh()"
        type="date"
      />
    </form>
  </div>

  <div class="table-responsive my-5">
  <table class="table w-100 table-bordered" *ngIf="productions.length > 0">
    <thead>
      <tr>
        <th class="ps-3 text-nowrap">Rif odoo</th>
        <th>Cliente</th>
        <!-- <th>Codice ordine</th> -->
        <th>Ordine-Riga</th>
        <!-- <th>Codice</th> -->
        <th>Data</th>
        <th>Stato</th>
        <!--
        <th scope="col">Scadenza</th> -->
      </tr>
    </thead>
    <tbody class="">
      <tr
        *ngFor="let p of productions?.reverse()"
        [routerLink]="['/production',p.id]"
      >
        <td class="ps-3">{{p.name}}</td>
        
        <!-- todo giulio -->
        <!-- <td>{{p.related_sale_order.value?.bs_shipping_partner}}</td> -->
         <!-- <td>
          {{getRelatedSale(p)?.partner_idid}}
         </td> -->
         
        <td>
          <!-- todo giulio -->
          {{getRelatedSale(p)?.partner_id?.name || 'N/A'}}
        </td>

        <td>
          <!-- <span class="badge bg-primary">{{p.product_qty}}</span> -->
          {{p.product_qty}} {{p.product_uom_id?.name}} -  {{p.product_id.name}} 
        </td>
		<!-- todo giulio -->
        <!-- <td>{{convertToInteger(p.related_sale_order_line.value?.bs_package_ordered || "")}}</td> -->
        <!-- <td>{{convertToInteger(p.related_sale_order_line.value?.bs_package || "")}}</td> -->
        <!-- <td>{{convertToInteger(p.related_sale_order_line.value?.bs_package_ordered || "")}}</td> -->
        <td>{{p.date_planned_start | date}}</td>
        <td [ngClass]="getStateClass(p)" class="text-nowrap">
          {{getStateLabel(p)}}
        </td>
      </tr>
    </tbody>
  </table>
</div>
</div>
</div>
