<div class="d-flex flex-column h-100">
    <app-nav></app-nav>
  
    <div class="d-flex flex-column h-100">
          
      <div class="d-flex align-items-center my-5 mx-4">
        <ol class="breadcrumb m-0 ">
          <li class="breadcrumb-item">
              <a [routerLink]="['/']">
                  <i class="fa fa-home"></i>
              </a>
          </li>
          <li class="breadcrumb-item">
                <a [routerLink]="['/warehouse']">Magazzino</a>
          </li>
          <li class="breadcrumb-item">
            <a>Produzione giornaliera</a>
          </li>
        </ol> 
      </div>
  
    <!-- <app-breadcrumb [config]="breadcrumb_config"></app-breadcrumb> -->
  <div class="container">
    <form class="mt-3 mx-3">
      <div class="row mt-3">
        <div class="col">
          Data
        </div>
        <div class="col ">
          <input
            type="date"
            name="date_planned_start"
            class="form-control"
            [(ngModel)]="day"
            (change)="filter()"
          />
        </div>
       </div>
       <!-- <div class="row mt-3">
        <div class="col ">
          Prodotto
        </div>
        <div class="col ">
          <select
            class="form-select"
            [(ngModel)]="modelForm.product"
            name="product"
            required="true"
            aria-label="Default select example"
          >
            <option *ngFor="let p of products" [ngValue]="p">{{p.name}}</option>
          </select>
        </div>
      </div> -->
      <!-- <div class="row mt-3">
        <div class="col ">
          Lotto
        </div>
        <div class="col ">
         <input name="lotname" class="form-control" required="required" >
        </div>
      </div> -->

      <br>
      <div class="row mt-2">
        <div class="col">
          <table class="table w-100 ">
            <thead>
              <tr>
                <th colspan="3">Da fare</th>
                <th class="text-end">
                    <a (click)="add()" class=" btn btn-primary">
                        <i class="fa fa-plus"></i>
                    </a>
                </th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let p of rows">
                <td (click)="remove(p)">
                    <i  class="fa-solid text-muted fa-trash-can"></i>
                  </td>
                <td class="w-75">
                    <select class="form-control w-100" [ngModel]="p.product_id.id" name="pid-{{p.id}}" (change)="updateProduct(p, $event)">
                      <option *ngFor="let pr of products" value="{{pr.id}}">{{pr.name}}</option>
                    </select>
                </td>
                <td nowrap  class="w-25">
                    
                    <span class="d-inline"> {{p.quantity - p.lot_ids.ids.length}} di </span>
                    <input
                      style="width:auto"
                      maxlength="4" 
                      type="number"
                      placeholder="Quantità"
                      class="form-control d-inline"
                      [ngModel]="p.quantity"
                      name="qty-{{p.id}}"
                      (change)="updateQty(p)"
                    />
                </td>
                <td>
                  <button class="btn btn-primary d-block" (click)="create(p)">Crea uno</button> 
                  <!-- <button *ngIf="!p.lot_id.id" class="btn btn-warning d-block">Crea</button>-->
                </td>
                
              </tr>
            </tbody>
          </table>
        </div>
          
      </div>
  
      <br>
      <br>
      <br>
  
      <div class="d-flex mt-3 justify-content-between ">
        <!-- <button type="button" *ngIf="!loading" [ngClass]="{'disabled': modelForm.pallets.length == 0}" class="btn btn-lg w-100 btn-success text-white" (click)="onSubmit()">
          CREA
        </button>
  
        <button type="button" disabled *ngIf="loading" class="btn btn-lg w-100 btn-success text-white" (click)="onSubmit()">
          <i class="fa fa-spinner"></i> CREA 
        </button> -->
  
      </div>
  
      
    </form>
  </div>
  </div>
  