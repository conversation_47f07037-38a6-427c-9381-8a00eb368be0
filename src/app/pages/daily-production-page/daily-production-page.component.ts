import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Data, Router, RouterLink } from '@angular/router';
import { firstValueFrom } from 'rxjs';
import { CONFIG } from 'src/app/CONFIG';
import { BassiDailyReplenishment } from 'src/app/models/bassi.daily_production_row';
import { ProductProduct } from 'src/app/models/product.product';
import { StockProductionLot } from 'src/app/models/stock.production-lot';
import { OdooEntityManager } from 'src/app/shared/services/odoo-entity-manager.service';

@Component({
  selector: 'app-daily-production-page',
  templateUrl: './daily-production-page.component.html',
  styleUrls: ['./daily-production-page.component.scss']
})
export class DailyReplenishmentPageComponent implements OnInit {
  rows: BassiDailyReplenishment[] = [];
  products: ProductProduct[] = [];
  day:Date =new Date(Date.now())
  loading: boolean;

  constructor(
    private odooEM:OdooEntityManager,
    private router:Router,
    private activatedRoute: ActivatedRoute
  ) { }

  async ngOnInit(): Promise<void> {
    this.load()
    this.products = await firstValueFrom(this.odooEM.search<ProductProduct>(new ProductProduct(),[['purchase_ok',"=",true], ['route_ids','in',CONFIG.reception_route_id]]))
  }
  
  async load() {
    this.rows = await firstValueFrom(
      this.odooEM.search<BassiDailyReplenishment>(new BassiDailyReplenishment(), [["date", "=", this.day]])
    )
  }

  async add() {
    await firstValueFrom(this.odooEM.create(new BassiDailyReplenishment(), {date: this._formatDate(Date.now())}))
    this.load()
  }

  async remove(t) {
    await firstValueFrom(this.odooEM.delete(new BassiDailyReplenishment(), [t.id]))
    await this.load()
  }

  async create(t:BassiDailyReplenishment) {
    this.router.navigate(['new', t.id], {relativeTo: this.activatedRoute})
  }

  filter() {
    this.load()
  }

  _formatDate(date) {
    var d = new Date(date),
        month = '' + (d.getMonth() + 1),
        day = '' + d.getDate(),
        year = d.getFullYear();

    if (month.length < 2) 
        month = '0' + month;
    if (day.length < 2) 
        day = '0' + day;

    return [year, month, day].join('-');
  }


  async updateProduct(r:BassiDailyReplenishment, ev) {

   await firstValueFrom(this.odooEM.update(r, {
    "product_id": ev.target.value
   }))
  }


  async updateQty(r:BassiDailyReplenishment) {
    this.loading = true
    await firstValueFrom(this.odooEM.update(r, {
     "quantity": r.quantity
    }))
   }
 
}
