// etichetta-sacco-blu.page.ts
import { ChangeDetectorRef, Component, ViewChild } from "@angular/core";
import { Router } from "@angular/router";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { StockQuantPackage } from "src/app/models/stock.quant.package";
import { OdooEntityManager } from "src/app/shared/services/odoo-entity-manager.service";
import { ToastService } from "src/app/toast/toast.service";
import { firstValueFrom, Observable  } from "rxjs";

@Component({
  selector: "app-etichetta-sacco-blu",
  templateUrl: "./etichetta-sacco-blu.page.html",
})
export class EtichettaSaccoBluPage {
  public quantCreating: boolean = false;
  loading: boolean = false;
  stockQuantPackages: StockQuantPackage[] = [];
  selectedStockQuantPackage: StockQuantPackage | null = null;
  newlyCreatedPackages: StockQuantPackage[] = [];
  filteredNewlyCreatedPackages: StockQuantPackage[] = [];
  public createdPackageIds: number[] = [];
  public lastCreatedPackages: any[] = [];
  selectedValue: number = 0;
  peso: number | null = null;
  public createdCount: number = 0;
  id?: number;
  name?: string;
  create_date?: string;
  tara?: number;
  package_type_id?: number;

  @ViewChild('taraModal') taraModal;
  isWeighing: boolean = false;

  constructor(
    private cdr: ChangeDetectorRef,
    private router: Router,
    private modalService: NgbModal,
    private toastr: ToastService,
    private odooEM: OdooEntityManager
  ) {}

  ngOnInit() {
  }

  async load() {
    this.loading = true;
    try {
      const stockQuants = await firstValueFrom(
        this.odooEM.search<StockQuantPackage>(new StockQuantPackage(), [
          ["package_type_id", "=", 35],
        ])
      );
  
      const today = new Date();
      const todayString = today.toISOString().split('T')[0];
  
      this.stockQuantPackages = stockQuants
        .filter(pkg => pkg.create_date?.startsWith(todayString))
        .sort((a, b) => {
          const dateA = new Date(a.create_date).getTime();
          const dateB = new Date(b.create_date).getTime();
          return dateB - dateA; // Newest first
        });
  
      this.createdCount = this.stockQuantPackages.length;
  
      if (this.stockQuantPackages.length > 0) {
        // Display all packages
        console.group("Most recent packages:");
        console.log(`Total created today: ${this.createdCount}`);
        // console.table(this.stockQuantPackages.map((pkg, index) => ({
        //   'Index': index,
        //   'ID': pkg.id,
        //   'Name': pkg.name,
        //   'Created Date': pkg.create_date,
        //   'Tara': pkg.tara > 0 ? 'Pesato' : 'Da pesare'
        // })));
      
        // Filter and display only newly created packages
        const newlyCreatedToday = this.stockQuantPackages.filter(pkg =>
          this.createdPackageIds.includes(pkg.id)
        );
      
        console.group("🟢 Filtered - Newly Created Packages:");
        // console.table(newlyCreatedToday.map((pkg, index) => ({
        //   'Index': index,
        //   'ID': pkg.id,
        //   'Name': pkg.name,
        //   'Created Date': pkg.create_date,
        //   'Tara': pkg.tara > 0 ? 'Pesato' : 'Da pesare'
        // })));
        console.groupEnd();
      }

      this.filteredNewlyCreatedPackages = this.stockQuantPackages.filter(pkg =>
        this.createdPackageIds.includes(pkg.id)
      );
      
  
    } catch (error) {
      console.error("Error loading packages:", error);
      this.toastr.error("Error loading today's packages", "Error");
    } finally {
      this.loading = false;
    }
  }

  openModal(item: StockQuantPackage) {
    this.selectedStockQuantPackage = item;
    // TODO GIULIO
    // this.selectedValue = item.tara;
    this.modalService.open(this.taraModal, { centered: true });
  }

  onPesaClick() {
    this.isWeighing = true;
  }

  onLineWeigh(value) {
    this.selectedValue = value;
    this.isWeighing = false;
  }

  async completa(modal: any) {
    await firstValueFrom(this.odooEM.update(this.selectedStockQuantPackage!, { tara: this.selectedValue }));
    this.toastr.success('Tara registrata correttamente!', 'Success');
    modal.close();
    this.load();
  }

  async confermaPeso() {
    const count = Number(this.peso);
    if (isNaN(count) || count <= 0) {
      this.toastr.error("Input non valido", "Errore");
      return;
    }
  
    this.quantCreating = true;
    this.lastCreatedPackages = [];
    let successCount = 0;
    
    try {
      for (let i = 0; i < count; i++) {
        try {
          // Create a type for the expected response
          interface PackageResponse {
            id: number;
            name: string;
            create_date?: string;
            [key: string]: any; // For any additional properties
          }
  
          // Get the response and properly type it
          const response = await firstValueFrom(
            this.odooEM.create(new StockQuantPackage(), { 
              package_type_id: 35 
            }) as Observable<PackageResponse>
          );
  
          // Now we can safely access the properties
          const createdPackage: PackageResponse = response;
  
          successCount++;
          this.createdPackageIds.push(createdPackage.id);
          this.lastCreatedPackages.push({
            id: createdPackage.id,
            name: createdPackage.name,
            create_date: createdPackage.create_date || new Date().toISOString()
          });
          
          if (i < count - 1) {
            await new Promise(resolve => setTimeout(resolve, 100));
          }
        } catch (error) {
          console.error(`Error creating package ${i + 1}:`, error);
          this.toastr.error(`Errore creazione pacchetto ${i + 1}`, "Errore");
        }
      }
  
      this.createdCount += successCount;
      
      console.log(`Created ${successCount} packages:`);
      console.table(this.lastCreatedPackages);
  
      if (successCount === count) {
        this.toastr.success(`Creati ${successCount} pacchetti correttamente!`, "Successo");
      } else {
        this.toastr.success(`Creati ${successCount} pacchetti su ${count}`, "Completato parzialmente");
      }
      
      await this.load();
    } catch (error) {
      console.error("Unexpected error:", error);
      this.toastr.error("Errore durante la creazione", "Errore");
    } finally {
      this.quantCreating = false;
      this.peso = null;
    }
  }
}