<!-- etichetta-sacco-blu.page.html -->
<div class="d-flex flex-column w-100 h-100">
    <app-nav></app-nav>

    <div class="d-flex justify-content-center align-items-center w-100 h-100" *ngIf="loading">
        <div class="spinner-border" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>

    <div class="mt-4" *ngIf="!loading">
        <div class="mx-auto d-flex gap-4 mt-4 px-3">
            <!-- Sidebar (25%) -->
            <div class="p-3 shadow-sm border rounded" style="width: 25%; height: 80vh;">
                <h5 class="mb-3">Etichette</h5>
                <ul class="list-group rounded shadow-sm" style="max-height: 75vh; overflow-y: auto;">
                    <li *ngFor="let item of filteredNewlyCreatedPackages"
                        class="list-group-item list-group-item-action cursor-pointer"
                        (click)="openModal(item)"
                        style="padding-block: 1rem; cursor: pointer;">
                      {{ item.name }}
                      <!-- TODO GIULIO -->
                      <!-- <span class="badge float-end" [ngClass]="item.tara > 0 ? 'bg-primary' : 'bg-danger'">
                        {{ item.tara > 0 ? 'Pesato' : 'Da pesare' }}
                      </span> -->
                    </li>
                  </ul>                  
            </div>

            <!-- Main content (75%) -->
            <div style="width: 75%; height: 80vh; overflow-y: hidden;"
                class="d-flex flex-column justify-content-center">
                <div class="p-5 text-center" style="max-width: 600px; margin-inline: auto;">
                    <h5 class="mb-4">Quante etichette da creare?</h5>
                    <div class="form-group mb-3">
                        <input type="number" class="form-control p-2" [(ngModel)]="peso"
                            placeholder="Inserisci il numero di etichette">
                    </div>
                    <button class="btn btn-success align-self-end" (click)="confermaPeso()" [disabled]="!peso || quantCreating">
                        <i [ngClass]="quantCreating ? 'fa fa-spinner fa-spin me-2' : 'fa fa-paper-plane me-2' "></i>
                        Conferma
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- <ng-template #taraModal let-modal>
    <div class="modal-header">
        <h5 class="modal-title">Tara</h5>
        <button type="button" class="btn-close" aria-label="Close" (click)="modal.dismiss()"></button>
    </div>
    <div class="modal-body text-center p-5">
        <button class="btn btn-secondary me-3" (click)="onPesaClick()">Pesa</button>
        <button class="btn btn-primary" (click)="completa(modal)">Completa</button>
    </div>
    <app-weigh-input *ngIf="isWeighing" (onWeight)="onLineWeigh($event)"></app-weigh-input>
</ng-template> -->