<div class="d-flex flex-column w-100 h-100">
  <app-nav></app-nav>

  <div class="px-5">
    <div class="d-flex align-items-center justify-content-between flex-wrap gap-4 mt-4">
      <ol class="breadcrumb m-0">
        <li class="breadcrumb-item">
          <a [routerLink]="['/']"><i class="fa fa-home"></i></a>
        </li>
        <li class="breadcrumb-item active">Selezione Prodotto</li>
      </ol>
    </div>
    
    <div class="d-flex align-items-center justify-content-center gap-5" style="height: 50vh;">
      <!-- Product Selection Card -->
      <div class="card product-card shadow-lg">
        <div class="card-header bg-primary text-white">
          <h6 class="mb-0"><i class="fas fa-box-open me-2"></i>Descrizione</h6>
        </div>
        <div class="card-body d-flex flex-column justify-content-center">
          <div class="form-floating">
            <select class="form-select" id="productSelect">
              <option selected disabled></option>
              <option>Disco con crosta</option>
              <option>Disco senza crosta</option>
              <option>Sfrido dolce</option>
              <option>Sfrido piccante</option>
              <option>Sfrido con crosta</option>
            </select>
            <label for="productSelect">Seleziona un prodotto</label>
          </div>
        </div>
      </div>
      
      <!-- Weight Selection Card -->
      <div class="card weight-card shadow-lg">
        <div class="card-header bg-success text-white">
          <h6 class="mb-0"><i class="fas fa-weight-hanging me-2"></i>Pesa</h6>
        </div>
        <div class="card-body d-flex flex-column justify-content-center align-items-center gap-5">
          <button class="btn btn-success btn-lg w-100 py-3 text-white" (click)="openKgModal()">
            <i class="fas fa-balance-scale me-2"></i>
            {{ selectedValue || 'Seleziona Peso' }}
          </button>
          <button class="btn btn-primary m-3 text-white" (click)="onItemConfirm()">
            Conferma
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Weigh Input Component -->
<app-weigh-input
  *ngIf="isWeighing"
  (onWeight)="onLineWeigh($event)"
></app-weigh-input>

<!-- Success Notification -->
<div *ngIf="showSuccessMessage"
     class="alert alert-success position-fixed top-0 end-0 mt-3 me-3 shadow"
     style="z-index: 1050; width: auto; min-width: 300px;"
     role="alert">
  Peso correttamente registrato!
</div>

