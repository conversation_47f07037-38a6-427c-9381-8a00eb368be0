import { Component } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Router } from '@angular/router';
import { ScartoDetailModalComponent } from "../../components/scarto-detail-modal/scarto-detail-modal.component"
import { ScartoConfirmModalComponent } from "../../components/scarto-confirm-modal/scarto-confirm-modal.component"
@Component({
  selector: 'app-scarto-detail',
  templateUrl: './scarto-detail.page.html',
  styleUrls: ['./scarto-detail.page.scss']
})
export class ScaetoDetail {
  selectedValue: any;
  isWeighing: boolean = false;
  selectedKg: number = 0;
  showSuccessMessage = false;

  constructor(private modalService: NgbModal, private router : Router) {}

  onItemConfirm() {
    this.showSuccessMessage = true;
  
    // Hide the message after 3 seconds and redirect
    setTimeout(() => {
      this.showSuccessMessage = false;
      this.router.navigate(['/scan-blu']);  // Add this line for redirection
    }, 1500);
  }

  openKgModal() {
    this.isWeighing = true;
  }

  onConfirm() {
    const modalRef = this.modalService.open(ScartoConfirmModalComponent);
    console.log("--------")
  }
  
  onLineWeigh(value) {
    console.log(value);
    this.selectedValue = value;
    this.isWeighing = false;
}
}