/* Add this to your component's CSS or global styles */
.product-card, .weight-card {
    width: 250px;
    height: 250px;
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 20px rgba(0,0,0,0.1) 
  }

  
  .product-card .card-header {
    border-radius: 15px 15px 0 0 !important;
  }
  
  .weight-card .card-header {
    border-radius: 15px 15px 0 0 !important;
  }
  
  .form-select {
    border: 2px solid #e9ecef;
    padding: 12px;
    font-size: 1rem;
  }
  
  .form-select:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
  }
  
  .alert-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1050;
    min-width: 350px;
    animation: slideIn 0.5s forwards;
  }
  
  @keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
  }
  
  .btn-lg {
    font-size: 1.1rem;
    border-radius: 10px;
    transition: all 0.3s ease;
  }


  .form-floating > .form-select {
    padding-top: 1.625rem;
    padding-bottom: 0.625rem;
  }
  
  .form-floating > .form-select ~ label {
    opacity: 0.65;
    transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
  }