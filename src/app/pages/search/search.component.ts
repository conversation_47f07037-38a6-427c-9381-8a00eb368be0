import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { first, firstValueFrom } from 'rxjs';
import { CONFIG } from 'src/app/CONFIG';
import { MrpProduction } from 'src/app/models/mrp.production.model';
import { ProductProduct } from 'src/app/models/product.product';
import { ProductTemplateAttributeValue } from 'src/app/models/product.template.attribute.value.model';
import { StockPicking } from 'src/app/models/stock.picking';
import { StockQuant } from 'src/app/models/stock.quant.model';
import { StockQuantPackage } from 'src/app/models/stock.quant.package';
import { OdooEntityManager } from 'src/app/shared/services/odoo-entity-manager.service';

@Component({
  selector: 'app-search',
  templateUrl: './search.component.html',
  styleUrls: ['./search.component.scss']
})
export class SearchComponent implements OnInit {


  package:StockQuantPackage|undefined = undefined
  pickings:StockPicking[] = []
  mo: MrpProduction;
  seasoningState: boolean;


  constructor(
    private odooEM:OdooEntityManager,
    private activatedRoute:ActivatedRoute
  ) { }

  ngOnInit(): void {

    this.activatedRoute.url.subscribe(async url => {
      var p = (await firstValueFrom(this.odooEM.search<StockQuantPackage>(new StockQuantPackage, [['id', '=', this.activatedRoute.snapshot.params['id']]])))[0]
      await firstValueFrom(this.odooEM.resolve(p.quant_ids)) 
      this.package  = p

      var qs = p.quant_ids.values;
      if (qs?.length) {
        this.mo = (await firstValueFrom(this.odooEM.search<MrpProduction>(new MrpProduction, [['lot_producing_id', '=', qs[0].lot_id.id]])))[0]
        this.pickings = await firstValueFrom(this.odooEM.search<StockPicking>(new StockPicking(), [['id', 'in', this.mo.picking_ids.ids]]))

        // get the starting date of seasoning if any
        var s = this.pickings.find(p => p.location_dest_id?.id == CONFIG.seasoning_location_id)
        if (s) {
          let q = this.package.quant_ids.values?.length ? this.package.quant_ids.values[0] : null;
          if (q) {
            await firstValueFrom(this.odooEM.resolveSingle<ProductProduct>(new ProductProduct(),q.product_id))

            let attrs = q.product_id.value.product_template_attribute_value_ids
            await firstValueFrom(this.odooEM.resolve<ProductTemplateAttributeValue>(attrs))
            let min = attrs.values?.find(a => a.product_attribute_value_id.name.includes('minim'))?.name
            let max = attrs.values?.find(a => a.product_attribute_value_id.name.includes('massim'))?.name
            

            console.log(s.date_done)
            let today = new Date();
            
            let date = new Date(s.date_done); // mese è zero-based, quindi 0 = Gennaio
            let dmin = new Date(date);
            dmin.setDate(date.getDate() + Number(min));
            let dmax = new Date(date);
            dmax.setDate(date.getDate() + Number(max));
            let nelRange = today >= dmin && today <= dmax;

            this.seasoningState = nelRange

            console.log("STAG ", nelRange, attrs, min, max)
          }
        }
        

      }
    });

  }

}
