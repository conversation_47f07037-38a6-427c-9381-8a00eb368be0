<div class="d-flex flex-column w-100 h-100">
<app-nav ></app-nav>


<div class="container-fluid d-flex flex-column">
    <div *ngIf="package" class="d-flex align-items-center justify-content-between flex-wrap gap-4 my-5">
     <ol class="breadcrumb m-0 ">
      <li class="breadcrumb-item">
        <a [routerLink]="['/']"><i class="fa fa-home"></i></a>
      </li>
      
       <li class="breadcrumb-item d-flex flex-wrap align-items-center">
           <a [routerLink]="['/search/' + package.id]">Collo {{package.name}} </a>
           <div>
               <span *ngIf="seasoningState == true" class="badge bg-primary ms-3">STAGIONATURA OK</span>
               <span *ngIf="seasoningState == false" class="badge bg-danger ms-3">STAGIONATURA</span>
           </div>
       </li>
      </ol> 
    </div>


    <div class="table-responsive my-5">
    <table class="table w-100" >
        <tbody>
          
        <tr>
            
            <td class="ps-4">Lotto</td>
            <td>
                <span *ngFor="let q of package?.quant_ids?.values">
                    {{q.product_id.name}} - {{q.lot_id.name}}
                </span>    
            </td>
        </tr>
        <tr>
            <td class="ps-4">Polivalente</td>
            <td>{{package?.poli_1}} - {{package?.poli_2}}</td>
        </tr>

        <!-- <tr>
            <td class="ps-4">Tara</td>
            <td>{{package?.tara}}</td>
        </tr> -->
        <!-- <tr>
            <td class="ps-4">Peso in ingresso</td>
            <td>{{package?.weight_before_room}}</td>
        </tr>
        <tr>
            <td class="ps-4">Peso in uscita</td>
            <td>{{package?.weight_after_room}}</td>
        </tr> -->
        
        <tr>
            <td class="ps-4">Ordine di produzione</td>
            <td>{{mo.name}}</td>
        </tr>

    </tbody>
    </table>
</div>


    <br><br><br>
    <h4 class="ms-4">
        Trasferimenti
    </h4>

    <div class="table-responsive my-5">
    <table class="table w-100">
        <tbody>
        <tr *ngFor="let p of pickings">
            <td class="ps-4">
                {{p.name}}
            </td>
            <td>
                {{p.location_id.name.replace("BA/Magazzino stagionatura/","")}}
            </td>
            <td>
                {{p.location_dest_id.name.replace("BA/Magazzino stagionatura/","")}}
            </td>
            <td>
                {{p.date_done}}
            </td>
        </tr>
    </tbody>
    </table>
</div>
</div>
</div>
