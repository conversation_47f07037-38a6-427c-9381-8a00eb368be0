import { Component, OnInit } from '@angular/core';
import { firstValueFrom } from 'rxjs';
import { MrpProduction } from 'src/app/models/mrp.production.model';
import { SaleOrderLine } from 'src/app/models/sale-order-line.model';
import { SaleOrder } from 'src/app/models/sale-order.model';
import { OdooEntityManager } from 'src/app/shared/services/odoo-entity-manager.service';
import { TranslateService } from 'src/app/shared/services/translate.service';
import { Router }          from '@angular/router';
import { Partner } from 'src/app/models/partner';
import { StockLocationRoute } from 'src/app/models/stock.location.route.model';
import { StockRule } from 'src/app/models/stock.rule.model';

@Component({
  selector: 'app-sales-page-browser',
  templateUrl: './sales.page.html'
})
export class SalesBrowserPage implements OnInit {
  productions: MrpProduction[];
  sales: SaleOrder[];
  saleOrderId: Number ;

  // breadcrumb_config: [string,string][]
  public idRicezione: number = 8

  orderLines: SaleOrderLine[] | undefined;
  stockLocationRoute: StockLocationRoute;

  constructor(private router : Router, private odooEM:OdooEntityManager, private translateService: TranslateService ) { }

  async ngOnInit(): Promise<void> {
    // this.breadcrumb_config = [["cruscotto","/"], ["ordini di vendita","./"]]
    // this.productions = await firstValueFrom(this.odooEM.search<MrpProduction>(new MrpProduction(), []))
    this.sales = await firstValueFrom(this.odooEM.search<SaleOrder>(new SaleOrder(), []))
    this.sales.forEach(async s => {
      await firstValueFrom(this.odooEM.resolveSingle<Partner>(new Partner(), s.partner_id))
    });


    var stockLocationRoute = await firstValueFrom(this.odooEM.search<StockLocationRoute>(new StockLocationRoute(), [["id", "=", this.idRicezione]]))
    await firstValueFrom(this.odooEM.resolveArray<StockRule,StockLocationRoute>(new StockRule(), stockLocationRoute, 'rule_ids'))
    this.stockLocationRoute = stockLocationRoute[0]

    
  }  

  async selectSaleOrder(id: Number){
    this.router.navigate(['/confezionamento/'+id.toString()]);
  }

  translate(s:string) {
    var t = this.translateService.translate(s)
    return t
  }
  
}
