<app-nav ></app-nav>

<div class="d-flex flex-column h-100">
  <!-- <app-nav [idWorker]="6" class=""></app-nav> -->

  <!-- <app-breadcrumb [config]="breadcrumb_config"></app-breadcrumb> -->

  <div class="card">

    
    <div class="card-header d-flex justify-content-between">
      <h4 class="mb-0">ORDINI DI VENDITA</h4>
    </div>

    <div class="card-body p-0">
      <table class="table w-100 table-hover  align-middle">
        <thead>
          <tr>
            <th scope="col" class="ps-3">Numero</th>
            <th scope="col">Cliente</th>
            <th scope="col">Scadenza</th>
          </tr>
        </thead>
        <tbody class="table-group-divider border-light">
          <tr *ngFor="let s of sales" (click)="selectSaleOrder(s.id)">
            <th class="ps-3" scope="row">{{s.name}}</th>
            <td>{{ s.partner_id.value.name}}</td>
            <td>{{s.date_order}}</td>

          </tr>
        </tbody>
      </table>
    </div>


  </div>
</div>


