import { Component, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { firstValueFrom } from 'rxjs';
import { ProductProduct } from 'src/app/models/product.product';
import { StockLocationRoute } from 'src/app/models/stock.location.route.model';
import { StockMoveLine } from 'src/app/models/stock.move.line';
import { StockPicking } from 'src/app/models/stock.picking';
import { StockQuantPackage } from 'src/app/models/stock.quant.package';
import { StockRule } from 'src/app/models/stock.rule.model';
import { OdooEntityManager } from 'src/app/shared/services/odoo-entity-manager.service';
import Bootstrap from 'bootstrap/dist/js/bootstrap';
import { CONFIG } from 'src/app/CONFIG';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { ScanPackageModal } from 'src/app/components/modals/scan-package/scan-package.modal';
import { StockPickingType } from 'src/app/models/stock.picking.type';
import { StockLocation } from 'src/app/models/stock.location.model';
import { PackageWeightTracking } from 'src/app/models/package.weight.tracking';

@Component({
  selector: 'app-picking',
  templateUrl: './picking-page.html',
})
export class PickingPage implements OnInit {


  picking: StockPicking;
  stockMoveLines: StockMoveLine[];
  pageName: string = ""
  warningModalCreated: boolean = false
  backOrderId: number;
  weighProperty: string;
  
  isWeighing: StockMoveLine|null = null;
  private checks: any;

  @ViewChild('modal') modal: ScanPackageModal;
  availableDestinations: StockLocation[]|null;
  packageOfInterest?: string;
  visible: any = false;
  packageWeights: PackageWeightTracking[] = [];

  
  
  constructor(
    public odooEM: OdooEntityManager, 
    public activatedRoute: ActivatedRoute,
    public router: Router,
    private http: HttpClient
  ) {}

  
  async ngOnInit(): Promise<void> {
    this.activatedRoute.url.subscribe(async url => {
      this.packageOfInterest = this.activatedRoute.snapshot.queryParams['p']
      await this.refreshPicking()
    });
  }

  async refreshPicking() {
    var p = (await firstValueFrom(this.odooEM.search<StockPicking>(new StockPicking(), [['id', '=', this.activatedRoute.snapshot.params['id']]])))[0]

      var t = p.picking_type_id.name.split(":")?.pop()
      if (t)
        this.pageName = t

    await firstValueFrom(this.odooEM.resolveArray<StockMoveLine, StockPicking>(new StockMoveLine(), [p], 'move_line_ids_without_package'))

    if (p.move_line_ids_without_package.values) {
      await firstValueFrom(this.odooEM.resolveArrayOfSingle<StockMoveLine>(new StockQuantPackage(), p.move_line_ids_without_package.values, 'result_package_id'))
      // p.move_line_ids_without_package.values.forEach(async (ml:StockMoveLine) => {

      //   await firstValueFrom(this.odooEM.resolveSingle<StockQuantPackage>(new StockQuantPackage(), ml.result_package_id))
      // })
    }

    await firstValueFrom(this.odooEM.resolveSingle(new StockPickingType(), p.picking_type_id))

    p.move_line_ids_without_package.values?.sort((a,b) => a.id-b.id)

    if (CONFIG.picking_types_with_destination.includes(p.picking_type_id.id)) {
      var locations = await firstValueFrom(this.odooEM.search<StockLocation>(new StockLocation(), [['location_id', '=', CONFIG.cameracalda_location_id]]))
      if (locations)
        this.availableDestinations = locations
    }
    
  
    if (p.picking_type_id.value.tracking_type) {
      var packagesids = p.move_line_ids_without_package.values?.map((x:StockMoveLine) => x.result_package_id.id)

      if (packagesids && packagesids.length > 0) {
        this.packageWeights = await firstValueFrom(this.odooEM.search<PackageWeightTracking>
          (new PackageWeightTracking(), [["package_id",'in',packagesids],['tracking_type', '=', p.picking_type_id.value.tracking_type]]));
      }

      console.log("packagesids", packagesids)
      // this.weight_type = 
    }


  
    // // obtain property from picking type
    // if (CONFIG.typemap[p.picking_type_id.id]) {
    //   this.weighProperty = CONFIG.typemap[p.picking_type_id.id]
    // }

    // // obtain checklist from picking type
    // if (CONFIG.checkmap[p.picking_type_id.id]) {
    //   this.checks = CONFIG.checkmap[p.picking_type_id.id]
    // }
    this.picking = p

  }


  getWeight(pack:StockQuantPackage) {
    return this.packageWeights.find(x => x.package_id.id == pack.id)?.weight
  }

  async weightLine(l) {
    this.isWeighing = l
  }

  async updateDestination(n:number, l:StockMoveLine) {
    await firstValueFrom(this.odooEM.update(l, {
      'location_dest_id': Number(n)
    }))

  }


  async onLineWeight(w:string|null) {
    console.log("onlinew")

    if (w == null) {
      this.isWeighing = null
      return
    }

    await firstValueFrom(this.odooEM.create<PackageWeightTracking>(new PackageWeightTracking(), {
      package_id: this.isWeighing?.result_package_id.id,
      tracking_type: this.picking.picking_type_id.value.tracking_type,
      weight: w
    }))
    this.isWeighing = null
    
    // if (!w) {
    //   this.isWeighing = null
    //   return  
    // }
    
    // if (!this.isWeighing)
    //   return
    
    // var l = this.isWeighing
    // this.isWeighing = null
    
    // l.result_package_id.value[this.weighProperty] = w
    // var x = {}
    // x[this.weighProperty] = w
    // await this.odooEM.update(l.result_package_id.value,x).toPromise()
    
  }

  async print(p:StockQuantPackage) {
    await firstValueFrom(this.http.put(`api/stock_quant_package/${p.id}/print_label`, {}, {headers : new HttpHeaders({ 'Content-Type': 'application/json' })})  )
  }

  
  allCompleted() {
    var find = true
    if(this.picking && this.picking.move_line_ids_without_package.values)    
      this.picking.move_line_ids_without_package.values.forEach(async (ml:StockMoveLine) => {
        if(ml.qty_done == 0) find = false
      })
    return find
  }

  openScanModal() {
    this.modal.show()
  }
  
  
  onBarcode(barcode: any) {
    this.picking?.move_line_ids_without_package?.values?.forEach(x => {
      console.log()
      if (barcode == x.result_package_id.name)
        this.completeLine(x)
    })
  }



  async validate() {
    
    // await this.odooEM.call(new MrpProduction(), 'action_generate_serial', null, null, this.mrpProduction.id).toPromise()

    // var x = await this.odooEM.call(new StockPicking(), "button_validate",null,null, this.picking.id).toPromise()
    
    // if (x && x.res_model && x.res_model == "stock.backorder.confirmation") {
    //   // if(!confirm("È stata consumata una quantità diversa da quella attesa dei seguenti prodotti. Confermare che è stato fatto intenzionalmente ?") )
    //   //   return

    //   this.warningBackorderModal.show()
    //   return
    // }else{
    //   if( this.warningBackorderModal._isShown && this.backOrderId != 0 )
    //     this.warningModalCreated = true
    // }
    

    await firstValueFrom(this.odooEM.call(
      new StockPicking(),
      "button_validate",
      null,
      null,
      Number(this.activatedRoute.snapshot.params['id'])
    ))

    this.router.navigate(["/warehouse/"])
  }

  async createBackOrder() {



    // var pickingCloned = Object.assign({}, this.picking) as {}
    // delete pickingCloned['ODOO_MODEL']
    // pickingCloned['name'] = '/'
    // pickingCloned['move_lines'] = []
    // pickingCloned['move_line_ids_without_package'] = []
    // pickingCloned['backorder_id'] = this.picking.id
    // pickingCloned['location_dest_id'] = this.picking.location_dest_id.id
    // pickingCloned['location_id'] = this.picking.location_id.id
    // pickingCloned['picking_type_id'] = this.picking.picking_type_id.id
    // var backorder_picking = await firstValueFrom(this.odooEM.create<StockPicking>(new StockPicking(), pickingCloned))
    
    // if(backorder_picking)
    // this.backOrderId = backorder_picking.id
    
    // if(this.picking.move_line_ids_without_package.values)
    // for await (const line of this.picking.move_line_ids_without_package.values) {

    //   await firstValueFrom(this.odooEM.resolveSingle<ProductProduct>(new ProductProduct(), line.product_id ))
    //   var lineCloned = Object.assign({}, line ) as {}
    //   delete lineCloned['ODOO_MODEL']
    //   lineCloned['qty_done'] = 0
    //   lineCloned['picking_id'] = backorder_picking?.id
    //   lineCloned['product_uom_qty'] = Number( line.product_uom_qty  ) - Number( line.qty_done )
    //   lineCloned['location_id'] = line.location_id.id
    //   lineCloned['lot_id'] = line.lot_id.id
    //   lineCloned['move_id'] = line.move_id.id
    //   lineCloned['package_id'] = line.package_id.id
    //   lineCloned['product_id'] = line.product_id.id
    //   lineCloned['result_package_id'] = line.result_package_id.id
    //   lineCloned['product_uom_id'] = line.product_id.value.uom_po_id.id
    //   lineCloned['location_dest_id'] = line.location_dest_id.id

    //   var l =  await firstValueFrom(this.odooEM.create<StockMoveLine>(new StockMoveLine(), lineCloned))
  
    //   await this.odooEM.update(line, {
    //     product_uom_qty: line.qty_done
    //   }).toPromise()
    // }  
    
    // console.log("back ",  backorder_picking)
    // await this.validate()


  }

  async setQty() {
    await firstValueFrom(this.odooEM.call(
      new StockPicking(),
      "action_set_quantities_to_reservation",
      null,
      null,
      Number(this.activatedRoute.snapshot.params['id'])
    ))
  }

  completeLine(ml:StockMoveLine) {
    this.odooEM.update(ml, {
      qty_done : ml.reserved_uom_qty
    })
    this.refreshPicking()
  }

  uncompleteLine(ml:StockMoveLine) {
    this.odooEM.update(ml, {
      qty_done : 0
    })
    this.refreshPicking()
  }


}
