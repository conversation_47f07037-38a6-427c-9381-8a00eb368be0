32
# Copyright (C) 2018 - TODAY, Open Source Integrators
# License AGPL-3.0 or later (http://www.gnu.org/licenses/agpl).
from odoo import SUPERUSER_ID, _, api, fields, models
from odoo import fields, models
import logging

_logger = logging.getLogger(__name__)

class MyStockPicking(models.Model):
    _inherit = "stock.picking"

    def create_backorder(self):



        """ This method is called when the user chose to create a backorder. It will create a new
        picking, the backorder, and move the stock.moves that are not `done` or `cancel` into it.
        """
        backorders = self.env['stock.picking']
        bo_to_assign = self.env['stock.picking']


        _logger.info('backorders %s',backorders)

        _logger.info('xxxXXX %s', self.id)

        # self.ensure_one()
        picking = self
        # _logger.warning(p)


        _logger.warning(picking)
        moves_to_backorder = picking.move_lines


        if moves_to_backorder:
            
            backorder_picking = picking.copy({
                'name': '/',
                'move_lines': [],
                'move_line_ids': [],
                'backorder_id': picking.id
            })

            _logger.warning('backorder picking %s', backorder_picking)

            # copy moveline
            for m in picking.move_ids_without_package:
                _logger.warning('move before copy%s', m)
                mc = m.copy({
                    'picking_id': backorder_picking.id,
                    'quantity_done' : 0, # attenzione perchè su db di slone è quantity_done, ma sul db di bassi nuovo si chiama qty_done
                })
                # for ml in m.move_line_nosuggest_ids:
                #     ml.copy({
                #         'move_id': mc.id
                #     })

            for m2 in picking.move_ids_without_package:
                m2.product_uom_qty = m2.quantity_done

            # picking.message_post(
            #     body=_('The backorder <a href=# data-oe-model=stock.picking data-oe-id=%d>%s</a> has been created.') % (
            #         backorder_picking.id, backorder_picking.name))
            # moves_to_backorder.write({'picking_id': backorder_picking.id})

            _logger.warning("XXXX DOPO")
            _logger.warning(moves_to_backorder)


            picking.action_assign()

            # moves_to_backorder.move_line_ids.package_level_id.write({'picking_id':backorder_picking.id})
            # moves_to_backorder.mapped('move_line_ids').write({'picking_id': backorder_picking.id})
            
            
            #backorders |= backorder_picking


            # if backorder_picking.picking_type_id.reservation_method == 'at_confirm':
            #     bo_to_assign |= backorder_picking
        
        # bo_to_assign.action_assign()

        return backorder_picking.id