<div class="d-flex flex-column h-100" *ngIf="picking">
  
  <app-nav></app-nav>

  <div class="d-flex align-items-center my-5 mx-4">
    <ol class="breadcrumb m-0 ">
      <li class="breadcrumb-item">
        <a [routerLink]="['/']"><i class="fa fa-home"></i></a>
      </li>
      
       <li class="breadcrumb-item">
           <a [routerLink]="['/warehouse']">Magazzino</a>
       </li>
       
       <li class="breadcrumb-item" *ngIf="picking?.picking_type_id?.id">
         <a [routerLink]="['/warehouse/pickings/', picking.picking_type_id.id]">{{pageName}}</a>
       </li>
       <li class="breadcrumb-item">
        <a><b>{{picking.origin}}</b></a>
      </li>
     </ol> 
    <a class="btn btn-primary ms-auto" [ngClass]="{ 'disabled': ( picking.state == 'done' || !allCompleted()  ) }"  (click)="validate()">COMPLETA</a>
   </div>
   
      <div class="table-responsive" *ngIf="picking">

        <table class="table  table-bordered w-100 align-middle">
          <thead class=" ">
            <tr>
              <th class="ps-3">Bancale</th>
              <th>Quantità</th>
              <th *ngIf="availableDestinations">Per</th>
              <th *ngIf="picking?.picking_type_id?.value?.tracking_type">Peso</th>
              <th *ngIf="picking.state != 'done'"></th>
            </tr>
          </thead>
          <tbody class="table-group-divider ">
            <tr 
              *ngFor="let l of picking?.move_line_ids_without_package?.values"
              [ngClass]="{'bg-warning':l.result_package_id.name == this.packageOfInterest}"
            >
                <td scope="row" class="px-4 text-nowrap">
                  <button class="btn btn-primary me-2">
                    <i (click)="print(l.result_package_id.value)" class="fa fa-print "></i>
                  </button> 
                  {{l.result_package_id.name}}
                </td>
                <td class="text-star px-4">{{l.reserved_uom_qty}}</td>

                <td *ngIf="availableDestinations">
                  <select style="width:8em" class="form-control" (ngModelChange)="updateDestination($event,l)" [(ngModel)]="l.location_dest_id.id">
                    <option *ngFor="let d of availableDestinations" [value]="d.id">
                      {{d.name}}
                    </option>
                  </select>
                </td>

                <td class="px-4" >
                  <ng-container *ngIf="picking?.picking_type_id?.value?.tracking_type">

                    <b (click)="weightLine(l)">{{getWeight(l.result_package_id.value) || 0}} KG</b>
                    <button 
                      *ngIf="l.qty_done == 0"
                      class="ms-2 btn btn-success text-white" 
                      (click)="weightLine(l)">
                      Pesa
                    </button>

                  </ng-container>
                  
                </td>
                
                <td class="text-center align-middle px-4" *ngIf="picking.state != 'done'">
                  <button *ngIf="l.qty_done == 0" class="btn btn-success text-white" (click)="completeLine(l)">Completa</button>
                  <button *ngIf="l.qty_done > 0" class="btn btn-link" (click)="uncompleteLine(l)">Annulla</button>
                </td>
              </tr>
          </tbody>
        </table>
  </div>
</div>

<div id="scanButton" class="d-flex justify-content-end me-4">
  <button (click)="openScanModal()" class="btn btn-primary">
    <i class="fa fa-scanner-gun fa-3x"></i>
  </button>
</div>

<div id="scanButton" class="d-flex justify-content-end me-4">
  <button (click)="openScanModal()" class="btn btn-primary">
      <i class="fa fa-scanner-gun fa-3x"></i>
  </button>
</div>

<app-scan-package-modal (onBarcode)="onBarcode($event)"></app-scan-package-modal>


  <app-weigh-input *ngIf="isWeighing" (onWeight)="onLineWeight($event)"></app-weigh-input>


<app-scan-package-modal #modal (onBarcode)="onBarcode($event)"></app-scan-package-modal>



<!-- 
<div class="modal" tabindex="-1" id="warningBackorder">
  <div class="modal-dialog modal-lg ">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">La produzione è minore della richiesta iniziale
        </h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>
      <div class="modal-body">

        <p>Creare un ordine residuo se l'elaborazione dei prodotti rimanenti è prevista in seguito. In caso contrario, non procedere alla creazione.</p>

      </div>
      <div class="modal-footer d-flex justify-content-between align-items-center">

        <ng-container *ngIf="!warningModalCreated">

          <button type="button" (click)="createBackOrder()" class="btn btn-primary" >Crea ordine residuo</button>
        </ng-container>
        <ng-container *ngIf="warningModalCreated">

          <div class="alert alert-primary" role="alert">
            Hai creato <a [routerLink]="['/picking/', backOrderId]" > un nuovo ordine residuo</a> in produzione.
          </div>

        </ng-container>       
        
      </div>
    </div>
  </div>
</div> -->

