<div class="d-flex flex-column w-100 h-100">
  <app-nav></app-nav>

  <div class="container-fluid d-flex flex-column" *ngIf="mrpProduction">

    <div class="d-flex align-items-center justify-content-between flex-wrap gap-4 my-5">
      <ol class="breadcrumb m-0 ">
        <li class="breadcrumb-item">
          <a [routerLink]="['/']"><i class="fa fa-home"></i></a>
        </li>

        <li class="breadcrumb-item">
          <a [routerLink]="['/production']">Produzione {{getTypeOfProduzione(mrpProduction)}}</a>
        </li>
        <li class="breadcrumb-item">
          <a>Ordine {{mrpProduction?.name}}</a> <span *ngIf="relatedSale"> ( {{relatedSale.partner_id.name}} )</span>
        </li>
      </ol>
      <div class="d-flex flex-wrap align-items-center gap-2">
        <!-- <button class="btn btn-success text-white"
          (click)="onPesaturaBancale()">Pesatura bancale</button>
        <button class="btn btn-success text-white"
          (click)="onTaraNuovoBancale()">Tara nuovo bancale</button> -->
        <button class="btn btn-warning" (click)="startJob(mrpProduction)" [disabled]="startingJob">
          <span *ngIf="startingJob" class="spinner-border spinner-border-sm me-2" role="status"
            aria-hidden="true"></span>
          {{ startingJob ? 'Avvio in corso...' : 'Avvio produzione' }}
        </button>

      

        <button [ngClass]="{'disabled': mrpProduction?.state == 'done'  }" class="btn btn-success text-white"
        (click)="openConfirmationModal()">Completa</button>
      </div>
    </div>
    <!-- <h5 class="ms-4 text-muted">Da produrre</h5> -->
<div class="d-flex">
  <div class="d-flex flex-column">
    <h4 class="ms-4 " *ngIf="orders && mrpProduction">
      {{mrpProduction.product_qty}} {{mrpProduction.product_id?.value?.name}}
    </h4>
    <!-- <h5 *ngIf="mrpProduction" class="ms-4">lotto di confezionamento: {{mrpProduction.lot_producing_id.name}}</h5> -->
  </div>
  
  <div class="ms-auto align-self-center">
    <!-- <button class="btn btn-primary" (click)="openScanModalForOutput()"> 
      <span *ngIf="getOutputPackage()">Associa collo</span>
      <span *ngIf="!getOutputPackage()">{{getOutputPackage()}}</span>
    </button> -->
  </div>
    
</div>


    <div class="d-flex gap-4 px-5" >

      <div class="d-flex flex-column justify-content-between flex-grow-1 p-0">
        <div class="card w-100 mt-5">
          <div class="card-header d-flex  align-items-center">
            Componenti
            <!-- <input [ngModel]="orders[0]?.qty_producing || ''" [ngModelOptions]="{'updateOn':'blur'}"
              (ngModelChange)="updateQtyProducing(orders[0],$event)" class="form-control">
            <button (click)="openScanModal()" class="btn btn-lg btn-link ms-auto me-2 text-primary"><i
                class="fa fa-search"></i>
            </button> -->
            <button (click)="openScanModal()" class="btn btn-lg btn-primary  me-2 ms-auto"><i class="fa fa-scanner-gun"></i>
            </button>
          </div>
          <div class="card-body p-0">
            <div class="table-responsive ">
              <table class="table w-100 table-striped table-bordered mb-0">
                <thead>
                  <tr>
                    <th class="w-25">Lotto</th>
                    <th class=" ">Collo</th>
                    <th class=" ">Utilizzato</th>
                    <th class=" ">Scarto</th>
                  </tr>
                </thead>
                <tbody>
                  <ng-container *ngFor="let move of mrpProduction?.move_raw_ids?.values">
                    <ng-container *ngFor="let line of move.move_line_ids.values">
                      <tr>  <!-- ngIf="line?.package_id === packageCode" -->
                        <td>
                          <div class="d-flex flex-column p-2">
                            <p>
                              {{ line.lot_id.name }}
                            </p>
                          </div>
                        </td>
                        <td>{{ line.package_id.name }}</td>
                        <td>
                          <button 
                            [ngClass]="{
                              'bg-success': scannedCode, 
                              'bg-danger': !scannedCode
                            }"
                            class="btn btn-lg text-white "
                            (click)="openInputModal(line, 'inputModal')">
                            {{ line.qty_done | number:'1.0-2':'it-IT' }} Forme
                          </button>
                        </td>
                        <td>
                          <!-- sacco scarto lo associo per lotto -->
                          <button 
                            *ngIf="!hasPackageScarto(line)"
                            class="btn btn-lg text-white btn-danger"
                            (click)="openInputModal(line, 'inputModal')">
                              <i class="fa fa-shopping-bag"></i>
                          </button>

                          <button 
                            *ngIf="hasPackageScarto(line)"
                            class="btn btn-success btn-lg text-white"
                            (click)="openInputModal(line, 'inputModal')">
                            <i class="fa fa-shopping-bag"></i>
                            {{getLineScarto(line)?.result_package_id?.name}}
                          </button>
                          

                          <button 
                            *ngIf="hasPackageScarto(line)"
                            (click)="activeLineScarto = line"
                            [ngClass]="{
                              'bg-success text-white': getLineScarto(line)?.qty_done,
                              'bg-warning text-dark': getLineScarto(line)?.qty_done === 0
                            }"
                            class="btn ms-3 btn-lg text-white"
                          >
                            {{getLineScarto(line)?.qty_done | number:'1.0-2':'it-IT' }} kg
                          </button>
                          
                        </td>
                      </tr>
                    </ng-container>
                  </ng-container>
                  </tbody>
              </table>
            </div>
          </div>
        </div>


        

        <!-- <div class="card w-100 mt-5">
          <div class="card-header d-flex align-items-center">
            Sacco blu

            <button class="btn btn-lg  ms-auto" (click)="openScanCodeModal()" style="background-color: #007bff;">
              <i class="fa fa-shopping-bag text-white"></i>
            </button>
          </div>
          <div class="card-body p-0">
            <div class="table-responsive ">
              <table class="table w-100 table-striped table-bordered mb-0">
                
                  <thead>
                    <tr>
                      <th  class="w-25">Prodotto</th>
                      <th class="">Scartato</th>
                    </tr>
                  </thead>
                  
                  <tbody>
                <ng-container *ngFor="let move of mrpProduction?.move_byproduct_ids?.values">
                  
                  <ng-container *ngFor="let line of move.move_line_ids.values">
                    <tr>  
                      <td class="px-3 text-nowrap" >
                            {{ line.product_id.name }}
                      </td>
                     
                      
                      <td >

                        <button 
                          [ngClass]="{
                            'bg-success': scannedCode, 
                            'bg-danger': !scannedCode
                          }"
                          class="btn btn-lg text-white "
                          (click)="openInputModal(line, 'inputModal')">
                          {{ line.qty_done | number:'1.0-2':'it-IT' }}
                        </button>

                      </td>
                    </tr>
                  </ng-container>
                </ng-container>
                
                </tbody>
              </table>
            </div>
          </div>
        </div> -->
      </div>

      <div *ngIf="isSemifinishedCategory" class="bg-light p-3 rounded shadow-sm" style="width: 25%; height: 100vh;">
        <h5 class="mb-3">Bancali</h5>
        <ul class="list-group">
            <li *ngFor="let package of stockQuants" class="list-group-item list-group-item-action cursor-pointer" style="padding-block: 1rem; cursor: pointer;">
              {{ package?.name }}
              <!-- TODO GIULIO -->
              <!-- <span class="badge float-end" [ngClass]="package?.tara! > 0 ? 'bg-primary' : 'bg-danger'">
                {{ package?.tara! > 0 ? 'Pesato' : 'Da pesare' }}
              </span> -->
            </li>
        </ul>
      </div>
    </div>

    <app-stock-quant-modal (onChoosed)="onChoosed($event)" [stockQuantArray]="stockQuantArray" #iModal modal_id="modal"
      type="number" title="titolo">
    </app-stock-quant-modal>

    <app-input-modal type="text" title="Imposta Quantità utilizzata" modal_id="inputModal"
      (onChange)="onChangeModalInput($event)"
      [defaultValue]="selectStockMoveLine?.qty_done"></app-input-modal>

    <app-scan-package-modal #modal (onBarcode)="onBarcode($event)"></app-scan-package-modal>
    
    <app-scan-package-modal #modalBarcodeForOutput (onBarcode)="onBarcodeForOutput($event)"></app-scan-package-modal>

    <app-scan-package-code-modal (onBarcode)="onBarcodeSaccoBlu($event)"></app-scan-package-code-modal>

    <!-- <app-scarto-detail-modal *ngIf="activeLineScarto" (confirmKg)="onConfirmKg($event)"> </app-scarto-detail-modal> -->

    <p-dialog [visible]="activeLineScarto ? true : false"  appendTo="body" header="Pesa scarto" [modal]="true" >
      <app-scarto-detail-modal (confirmKg)="onConfirmKg($event)"></app-scarto-detail-modal>
    </p-dialog>

    <div class="modal" tabindex="-1" id="warningBackorder">
      <div class="modal-dialog modal-lg ">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">La produzione è minore della richiesta iniziale
            </h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">

            <p>Creare un ordine residuo se l'elaborazione dei prodotti rimanenti è prevista in seguito. In caso
              contrario,
              non procedere alla creazione.</p>

          </div>
          <div class="modal-footer d-flex justify-content-between align-items-center">

            <ng-container *ngIf="!warningModalCreated">

              <!-- <button type="button" (click)="createBackOrder()" class="btn btn-primary">Crea ordine residuo</button> -->
              <button type="button " (click)="confirmNoBackOrder()" class="btn btn-primary">Nessun ordine
                residuo</button>
            </ng-container>
            <ng-container *ngIf="warningModalCreated">

              <div class="alert alert-primary" role="alert">
                Hai creato <a [routerLink]="['/production/', backOrderId]"> un nuovo ordine residuo</a> in produzione.
              </div>

            </ng-container>

          </div>
        </div>
      </div>
    </div>

  </div>

</div>


