<div class="d-flex flex-column w-100 h-100">
    <app-nav></app-nav>

    <div class="px-5">
        <div class="d-flex flex-column align-items-center justify-content-center" *ngIf="!barcode"
            style="height: 80vh;">
            <div class="barcode-input-wrapper text-center w-25">
                <p>
                    <span>Scansiona il codice a barre del bancale</span>
                </p>
                <div class="d-flex gap-2">
                    <input class="form-control" type="text" #barcodeInput />
                    <button class="btn btn-primary text-white" (click)="onBarcodeScan(barcodeInput.value)">
                        <i class="fa fa-search"></i>
                    </button>
                </div>
            </div>
        </div>

        <div class="d-flex align-items-center justify-content-between flex-wrap gap-4 mt-4" *ngIf="barcode">
            <ol class="breadcrumb m-0 ">
                <li class="breadcrumb-item">
                    <a [routerLink]="['/']"><i class="fa fa-home"></i></a>
                </li>

                <li class="breadcrumb-item">
                    <a [routerLink]="['/pesatura-bancale?barcode=' + barcode]">Bancali {{barcode}}</a>
                </li>
            </ol>
        </div>

        <div class="d-flex flex-column justify-content-between gap-4 w-100 h-100 mt-5" *ngIf="barcode">
            <table class="table table-hover">
                <thead class="">
                    <tr>
                        <th>Bancale</th>
                        <th>Quantita</th>
                        <th>Pesa</th>
                        <th>Azione</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            {{ barcode }}
                        </td>
                        <td>
                            48
                        </td>
                        <td>
                            <button class="btn btn-primary d-flex" (click)="onPesaClick()">
                                {{ selectedValue || 'Select' }}
                            </button>
                        </td>
                        <td>
                            <button class="btn btn-primary d-flex" (click)="onConfirm()">
                                Conferma
                            </button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<app-weigh-input *ngIf="isWeighing" (onWeight)="onLineWeigh($event)"></app-weigh-input>