import { Component } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
// import { CalculatorModalComponent } from 'src/app/components/calculator-modal/calculator-modal.component';
import { ToastService } from 'src/app/toast/toast.service';
import { WeighInputComponent } from 'src/app/components/weigh-input/weigh-input.component';
@Component({
    selector: 'app-pesatura-bancale',
    templateUrl: './pesatura-bancale.page.html',
})
export class PesaturaBancalePage {
    barcode: string | null = null;
    selectedValue: number = 0;
    isWeighing: boolean = false;

    constructor(private router: Router, private modalService: NgbModal, private activatedRoute: ActivatedRoute, private toastr: ToastService) { }

    getBarcode() {
        return this.activatedRoute.snapshot.queryParamMap.get('barcode');
    }

    onBarcodeScan(barcodeValue: string) {
        if (barcodeValue.trim()) {
            this.router.navigate(['/pesatura-bancale'], { queryParams: { barcode: barcodeValue } });
        }
    }

    ngOnInit() {
        this.activatedRoute.queryParamMap.subscribe(params => {
            const barcode = params.get('barcode');
            if (barcode) {
                this.barcode = barcode;
                console.log("Scanned barcode:", barcode);
            }
        });
    }

    onPesaClick() {
        this.isWeighing = true;
    }

    onLineWeigh(value) {
        console.log("onLineWeigh", value);
        this.selectedValue = value;
        this.isWeighing = false;
    }

    onConfirm() {
        console.log("Value: ", this.selectedValue);
        
        this.barcode = null;
        // success toast message: peso correttemante registrato!
        this.toastr.success('Peso correttamente registrato!', 'Success');
    }
}