import { ChangeDetectorRef, Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { first, firstValueFrom } from 'rxjs';
import { MrpProduction } from 'src/app/models/mrp.production.model';
import { Partner } from 'src/app/models/partner';
import { StockMove } from 'src/app/models/stock.move';
import { OdooEntityManager } from 'src/app/shared/services/odoo-entity-manager.service';
import Bootstrap from 'bootstrap/dist/js/bootstrap';
import { InputModal } from 'src/app/components/modals/input/input.modal';
import { StockLocationRoute } from 'src/app/models/stock.location.route.model';
import { StockRule } from 'src/app/models/stock.rule.model';
import { StockQuant } from 'src/app/models/stock.quant.model';
import { StockQuantPackage } from 'src/app/models/stock.quant.package';
import { StockMoveLine } from 'src/app/models/stock.move.line';
import { ProductProduct } from 'src/app/models/product.product';
import { StockProductionLot } from 'src/app/models/stock.production-lot';
import { MrpProductionBackorder } from 'src/app/models/mrp.production.backorder.model';
import { MrpProductionBackorderLine } from 'src/app/models/mrp.production.backorder.line.model';
import { MrpConsumptionWarning } from 'src/app/models/mrp.consumption.warning.model';
import { Router } from '@angular/router';
import { SaleOrder } from 'src/app/models/sale-order.model';
import { ScanPackageModal } from 'src/app/components/modals/scan-package/scan-package.modal';
import { OdoorpcService } from 'src/app/shared/services/odoorpc.service';
import { CONFIG } from 'src/app/CONFIG';
import { ToastService } from 'src/app/toast/toast.service';
import { ScanPackageCodeModal } from 'src/app/components/modals/scan-package-code/scan-package-code.modal';
import { ProductTemplate } from 'src/app/models/product.template';
import { SaleOrderLine } from 'src/app/models/sale-order-line.model';

@Component({
  selector: 'app-zolamasca',
  templateUrl: './production-zolamasca.page.html',
})
export class ProductionZolaMascaPage implements OnInit {
  breadcrumb_config: [string, string][]

  mrpProduction: MrpProduction;
  showLines: Boolean = false
  @ViewChild('iModal', { static: true }) inputModal: InputModal;
  // public idRicezione: number = 8
  stockLocationRoute: StockLocationRoute;
  stockQuantArray: StockQuant[];
  generatedSerial: boolean = false
  selectStockMoveLine: StockMoveLine;
  selectStockMove: StockMove;
  packageCode: string | null = null;
  stockMoves: StockMove[] = [];
  stockMoveLines: StockMoveLine[] = [];
  qtyProductionModal: Bootstrap.Modal;
  moveLineQtyModal: Bootstrap.Modal;
  warningBackorderModal: Bootstrap.Modal;
  error: boolean;
  backOrderId: any;
  warningModalCreated: boolean = false
  singleLotName: any;
  producingProduct: any;
  relatedSale: SaleOrder;

  isSemifinishedCategory: boolean = false;

  confirmationModal: any;

  @ViewChild('modal') modal: ScanPackageModal;
  @ViewChild(ScanPackageCodeModal) scanModal!: ScanPackageCodeModal;

  scannedCode: string | null = null;

  startingJob: boolean;
  itemList: { id: number; name: string; weighted: boolean }[] = [
    { id: 1, name: 'IO439934', weighted: false },
    { id: 2, name: 'IO439935', weighted: false },
    { id: 3, name: 'IO439936', weighted: true },
    { id: 4, name: 'IO439937', weighted: true },
    { id: 5, name: 'IO439938', weighted: false },
    { id: 6, name: 'IO439939', weighted: true },
    { id: 7, name: 'IO439940', weighted: false },
    { id: 8, name: 'IO439941', weighted: true },
    { id: 9, name: 'IO439942', weighted: false },
    { id: 10, name: 'IO439943', weighted: false },
  ];
  stockQuants: StockQuantPackage[];

  constructor(private odoorpcService: OdoorpcService, private elementRef: ElementRef, public router: Router, private cd: ChangeDetectorRef, private toast: ToastService, public odooEM: OdooEntityManager, public activatedRoute: ActivatedRoute) { }

  async ngOnInit(): Promise<void> {


    this.activatedRoute.url.subscribe(async url => {
      await this.load()
      // load product to add to byproducts with category like "semilavorato"
    });

    this.scannedCode = localStorage.getItem('scannedCode');
  }

  async openConfirmationModal() {
    if (!confirm("Confermi di chiudere la produzione?"))
      return

    await firstValueFrom(this.odooEM.call(new MrpProduction(), 'action_generate_serial', null, null, this.mrpProduction.id))

    await firstValueFrom(this.odooEM.update(this.mrpProduction, {
      "qty_producing": this.mrpProduction.product_qty
    }))


    await firstValueFrom(this.odooEM.call(new MrpProduction(), 'button_mark_done', null, null, this.mrpProduction.id))
  }

  isPackageWeighted(id: number) {
    if (!id) return;
    const stockQuant = this.stockQuants.find(s => s.id?.toString() === id?.toString())!
    // console.log(this.stockQuants);
    return false
    // tODO GIULIO
    // return stockQuant?.tara > 0;
  }

  async load() {
    var p = (await firstValueFrom(this.odooEM.search<MrpProduction>(new MrpProduction(), [
      ['id', '=', this.activatedRoute.snapshot.params['id']]
    ])))[0]

    // await this.resolveProduction(p)
    // load order and backorders

    await firstValueFrom(this.odooEM.resolveSingle(new ProductProduct(), p.product_id));
    await firstValueFrom(this.odooEM.resolve(p.move_raw_ids));
    await firstValueFrom(this.odooEM.resolve(p?.move_byproduct_ids));


    if (p.product_id?.value?.product_tmpl_id) {
      await firstValueFrom(this.odooEM.resolveSingle(new ProductTemplate(), p.product_id?.value?.product_tmpl_id));
    }
    if (p.move_raw_ids.values) {
      this.stockMoves = await firstValueFrom(this.odooEM.resolveArray(new StockMoveLine(), p.move_raw_ids.values, "move_line_ids")) || [];
    }

    console.log("xx",p.move_byproduct_ids.values);
    if (p?.move_byproduct_ids?.values?.length) {
      await firstValueFrom(this.odooEM.resolveArray(new StockMoveLine(), p?.move_byproduct_ids.values, "move_line_ids"))
    }

    // await firstValueFrom(this.odooEM.resolveSingle(new SaleOrderLine(), p?.related_sale_order_line))
    this.mrpProduction = p
    const categoryName = this.mrpProduction?.product_id?.value?.product_tmpl_id?.value?.categ_id?.name;
    this.isSemifinishedCategory = categoryName === 'All / Semifinished';

    console.log("Category name:", categoryName);
    console.log("isSemifinishedCategory:", this.isSemifinishedCategory);

    if (!this.mrpProduction.lot_producing_id.id) {
      var x = await this.odooEM.call(new MrpProduction(), 'action_generate_serial', null, null, this.mrpProduction.id).toPromise()
    }

    const packageIds = this.mrpProduction?.move_finished_ids?.values?.[0]?.move_line_ids?.values?.map(ml => ml.package_id.id) || [];
    const stockQuantPackages = await firstValueFrom(this.odooEM.search<StockQuantPackage>(new StockQuantPackage(), [
      ['id', 'in', packageIds],
    ]));

    this.stockQuants = stockQuantPackages;
    console.log("stockQuants: ", this.stockQuants);

    console.log("MRP.Productions: ", this.mrpProduction);
  }

  async resolveProduction(p: MrpProduction) {
    var bos = await firstValueFrom(this.odooEM.search<MrpProduction>(new MrpProduction(), [
      ['name', 'like', p.name.split("-")[0] + "%"]
    ]))
    // we work on all the orders and backorder, mrpProduction is the first one

    // search related order
    var ss = await firstValueFrom(this.odooEM.search<SaleOrder>(new SaleOrder(), [['name', '=', bos[0].origin]]))
    if (ss.length)
      this.relatedSale = ss[0]
    await this.odooEM.resolve(p.move_raw_ids).toPromise()
    await this.odooEM.resolveArray(new StockMove(), bos, "move_raw_ids").toPromise()
    if (p.move_raw_ids.values)
      await this.odooEM.resolveArray(new StockMoveLine(), p.move_raw_ids.values, "move_line_ids").toPromise()

    var moves: StockMove[] = []
    bos.forEach(o => {
      if (o.move_raw_ids.values)
        moves = moves.concat(o.move_raw_ids.values)
    })


    await this.odooEM.resolveArray(new StockMoveLine(), moves, "move_line_ids").toPromise()

    // simplify no- we are picking just one product. the one we find in the first move of the first order 
    // this.producingProduct = moves[0].product_id.value
    this.orders = [p]
  }

  onPesaturaBancale() {
    this.router.navigate(['/pesatura-bancale'])
  }

  onTaraNuovoBancale() {
    this.router.navigate(['/tara-nuovo-bancale'])
  }


  async generateSerial(o) {
    var x = await this.odooEM.call(new MrpProduction(), 'action_generate_serial', null, null, o.id).toPromise()
    // temp for template , the call action_generate_serial non mi restituisce nulla
    this.cd.detectChanges();
  }

  async confirmNoBackOrder() {
  }


  async addToByProducts(name: string, saccoBlu?: StockQuantPackage) {

    // Cerca il prodotto scarto basato sul nome del prodotto originale
    let res = await firstValueFrom(this.odooEM.search<ProductProduct>(
      new ProductProduct(), [['name', '=', "Scarto " + name]]))
    
    if (!res.length) {
      this.toast.error(name + ": non esiste il prodotto scarto!");
      return;
    } 

    let scarto = res[0];

    // Crea un nuovo movimento di byproduct
    let move = await firstValueFrom(this.odooEM.create<StockMove>(new StockMove(), {
      name: scarto.name,
      production_id: this.mrpProduction.id,  // Collegamento alla produzione corrente
      product_id: scarto.id,
      product_uom: scarto.uom_id.id,
      location_id: CONFIG.confezionamento_location,
      location_dest_id: CONFIG.confezionamento_location,
    }));

    if (!move?.id) {
      this.toast.error("Errore nella creazione del movimento byproduct!");
      return;
    }

    // Crea la linea di movimento associata al sacco blu
    await firstValueFrom(this.odooEM.create<StockMoveLine>(new StockMoveLine(), {
      move_id: move.id,
      product_id: scarto.id,
      product_uom_id: scarto.uom_id.id,
      location_id: CONFIG.confezionamento_location,
      location_dest_id: CONFIG.confezionamento_location,
      result_package_id: saccoBlu ? saccoBlu.id : undefined
    }));

    this.toast.success("Scarto aggiunto correttamente!");
    
    // Ricarica i dati per mostrare il byproduct aggiunto
    await this.load();
  }

  getSaccoBlu() {
    let x = this.mrpProduction?.move_byproduct_ids?.values?.length && 
      this.mrpProduction?.move_byproduct_ids?.values[0].move_line_ids.values?.length
      && this.mrpProduction?.move_byproduct_ids?.values[0].move_line_ids.values[0].result_package_id.value
    return x || null
  }

  // 
  async createBackOrder(o: MrpProduction): Promise<number | null> {

    await firstValueFrom(this.odooEM.update<MrpProduction>(this.mrpProduction, {
      "qty_producing": 0.1
    }))

    var backorder = await firstValueFrom(this.odooEM.create<MrpProductionBackorder>(new MrpProductionBackorder(), {
      "mrp_production_ids": [o.id]
    }))

    if (!backorder) {
      this.toast.error("Errore nella creazione del backorder!");
      return null
    }

    await firstValueFrom(this.odooEM.create<MrpProductionBackorderLine>(
      new MrpProductionBackorderLine(),
      {
        "mrp_production_id": o.id,
        "mrp_production_backorder_id": backorder.id,
        "to_backorder": true
      }
    ))

    var x = await this.odooEM.call(new MrpProductionBackorder(), 'action_backorder', null, null, [backorder.id]).toPromise()

    if (x.error) {
      this.toast.error(x.error.data.message || "Errore nella creazione del backorder!");
      return null
    }

    // set the producing_lot_id del backorder
    // let b  = new MrpProduction(x.res_id)
    // await firstValueFrom(this.odooEM.update<MrpProductionBackorder>(b, {
    //   "lot_producing_id" : o.lot_producing_id.id
    // }))

    // await this.markDone()
    return x.res_id

    return null
  }


  async openQtyProductionModal() {
    this.qtyProductionModal = new Bootstrap.Modal(document.getElementById('inputModal2'))
    this.qtyProductionModal.show()
  }


  async openScanModal() {
    // this.selectStockMove = m
    this.modal.show()
  }

  openScanCodeModal(): void {
    this.scanModal.show();
  }

  // handleScannedCode(code: string) {
  //   console.log("Scanned Code: ", code);
  //   if(!code || !code?.startsWith("P")) {
  //     this.toast.error("Codice non valido!");
  //     return;
  //   }

  //   this.toast.success("Codice scansionato correttamente!");
  //   this.scanModal.hide();
  // }

  // onBarcodeScan(barcode: string): void {
  //   console.log('Scanned barcode:', barcode);
  //   // Handle the scanned barcode
  // }

  openInputModal(s, id) {
    const scanned = localStorage.getItem('scannedCode');
    if (scanned) {
      s.qty_done = 1;
      this.scannedCode = scanned;
      localStorage.removeItem('scannedCode');
    }

    this.selectStockMoveLine = s;
    this.moveLineQtyModal = new Bootstrap.Modal(document.getElementById('inputModal'));
    this.moveLineQtyModal.show();

    setTimeout(() => {
      this.elementRef.nativeElement.querySelector("#" + id).querySelector("#input")?.focus();
    }, 1);
  }


  async onBarcodeSaccoBlu(code: string) {
    // this.packageCode = code;
    let quant_package = await firstValueFrom(this.odooEM.search<StockQuantPackage>(new StockQuantPackage(), [['name', '=', code]]));
    if (!quant_package.length) {
      this.error = true;
      this.toast.error("Non trovato o codifica errata!");
      return;
    }

    this.mrpProduction.move_raw_ids.values?.forEach(async (m: StockMove) => {
      this.addToByProducts(m.product_id.name, quant_package[0]);
    })


  }

  async onBarcode(code: string) {
    console.log("Scanned Package Code: ", code);
    this.packageCode = code;
    let quant_package = await firstValueFrom(this.odooEM.search<StockQuantPackage>(new StockQuantPackage(), [['name', '=', code]]));
    if (!quant_package.length) {
      this.error = true;
      this.toast.error("Non trovato o codifica errata!");
      return;
    }

    await firstValueFrom(this.odooEM.resolve(quant_package[0]?.quant_ids));

    console.log(quant_package);

    if (quant_package[0]?.quant_ids.values && quant_package[0]?.quant_ids.values.length) {
      for (var sq of quant_package[0]?.quant_ids?.values) {
        await this.handleSingleLot(sq);

        this.addToByProducts(sq.product_id.name, this.getSaccoBlu() || undefined);
        break;
      }
    } else {
      this.toast.error("Nessuna quantità nel pacco!");
    }

    await this.load();
  }


  orders = [{ qty_producing: 0 }]; // Example orders array

  // Function to update qty_producing and remove the value after 1 second
  updateQtyProducing(order: any, newValue: number): void {
    // Update the order's qty_producing with the new value
    order.qty_producing = newValue;

    // Set a timeout to reset the value after 1 second
    setTimeout(() => {
      order.qty_producing = ""; // Reset the value (or you can set it to another value)
    }, 10); // 1000 milliseconds = 1 second
  }


  async startJob(o: MrpProduction) {
    this.startingJob = true;

    const minDelay = new Promise(resolve => setTimeout(resolve, 3000));

    const params = {
      model: new MrpProduction().ODOO_MODEL,
      method: "start_job",
      args: [[o.id]],
      kwargs: {
        context: null
      }
    };

    try {
      const request = this.odoorpcService.sendRequest(
        `/api/web/dataset/call_kw/${params.model}/${params.method}`,
        params
      );

      const [response] = await Promise.all([request, minDelay]);
      console.log('Job started:', response);
      this.toast.success('Produzione avviata con successo!');
    } catch (err) {
      console.error('Error starting job:', err);
      this.toast.error('Failed to start job');
    } finally {
      this.startingJob = false;
    }
  }




  async handleSingleLot(sq: StockQuant) {
    if (sq.quantity <= 0) return;


    let ms = this.mrpProduction.move_raw_ids.values?.filter(m => m.product_id.id == sq.product_id.id)

    // nn trovo la move
    if (!ms?.length) {
      let x = await firstValueFrom(this.odooEM.create<StockMove>(new StockMove(), {
        name: sq.product_id.name,
        raw_material_production_id: this.mrpProduction.id,
        product_id: sq.product_id.id,
        product_uom: sq.product_uom_id.id,
        location_id: CONFIG.confezionamento_location,
        location_dest_id: CONFIG.confezionamento_location
      }))
      if (x)
        ms = [x]
    }
    await firstValueFrom(this.odooEM.resolveSingle<ProductProduct>(new ProductProduct(), sq.product_id))


    if (ms) {
      let ml = await firstValueFrom(this.odooEM.create<StockMoveLine>(new StockMoveLine(), {
        move_id: ms[0].id,
        product_id: sq.product_id.id,
        product_uom_id: sq.product_uom_id.id,
        lot_id: sq.lot_id.id,
        package_id: sq.package_id.id,
        location_id: CONFIG.confezionamento_location,
        location_dest_id: CONFIG.confezionamento_location
      }))
    }
  }

  // nel caso non abbiamo un lotname nell'order di vendita
  async handleMultiLot(sq: StockQuant) {

    let o = this.mrpProduction

    console.log("HANDLE MULTI", o)
    let moves = o?.move_raw_ids.values

    // todo diam per buon si usi un solo prodotto

    // se non ho delle move
    if (!moves?.length) {
      this.handleSingleLot(sq)
    }
    // se e' lo stesso lotto
    else if (moves?.length
      && moves[0].move_line_ids.values
      && moves[0].move_line_ids.values[0].lot_id.name == sq.lot_id.name) {
      this.handleSingleLot(sq)
    }
    // se ho delle move chiudo l'ordine e creo un backorder
    else if (moves?.length) {
      if (!confirm("Confermi inizio nuovo lotto ?"))
        return

      await firstValueFrom(this.odooEM.update<MrpProduction>(o, {
        "qty_producing": 0.1
      }))

      await firstValueFrom(this.odooEM.call(
        new MrpProduction(),
        "action_confirm",
        null,
        null,
        this.mrpProduction.id
      ))

      var bid = await this.createBackOrder(o)
      if (!bid)
        return this.toast.error("Errore nella creazione del backorder!");

      // fetch the fresh backorder, use it to handle lot and redirect 
      let bs = await firstValueFrom(this.odooEM.search<MrpProduction>(new MrpProduction(), [['id', '=', bid]]))
      if (bs.length) {

        let ls = await firstValueFrom(this.odooEM.search<StockProductionLot>(new StockProductionLot(),
          [['name', 'ilike', sq.lot_id.name], ['product_id', '=', bs[0].product_id.id]]))

        if (ls?.length) {
          // se ho gia il lotto
          await firstValueFrom(this.odooEM.update(bs[0], {
            lot_producing_id: ls[0].id
          }))
        } else {
          // devo crearlo
          let l = await firstValueFrom(this.odooEM.create<StockProductionLot>(new StockProductionLot(), {
            company_id: 1,
            product_id: bs[0].product_id.id,
            name: sq.lot_id.name
          }))

          if (l)
            await firstValueFrom(this.odooEM.update(bs[0], {
              lot_producing_id: l.id
            }))
        }

        // await firstValueFrom(this.odooEM.update(bs[0], {lot_producing_id: this.mrpProduction}))

        this.mrpProduction = bs[0]
        await this.handleSingleLot(sq)
        this.router.navigate(['production', bid])
      } else
        return this.toast.error("Errore nella creazione del backorder!");
    }
  }

  async markDone() {
    try {
      await this.odooEM.call(new MrpProduction(), 'button_mark_done', null, null, this.mrpProduction.id).toPromise()

      await firstValueFrom(this.odooEM.create(new MrpConsumptionWarning(), {
        mrp_production_ids: [[6, false, [this.mrpProduction.id]]]
      }))
    } catch (e) {
      this.toast.error("Errore nell'invio della notifica!");
      return
    }
    // await this.load()
  }


  groupByLot(o: MrpProduction): string[] {
    console.log("group by lot", o)
    let xx: string[] = []
    o.move_raw_ids.values?.forEach(m => {

      m.move_line_ids.values?.forEach(ml => {
        console.log("grou 2 ", ml)
        if (!xx.includes(ml.lot_id.name))
          xx.push(ml.lot_id.name)
      })

    })
    return xx
  }

  getLinesByLot(o: MrpProduction, lot: string) {
    let r: StockMoveLine[] = []
    o.move_raw_ids.values?.forEach(m => {
      m.move_line_ids.values?.forEach(ml => {
        if (ml.lot_id.name == lot)
          r.push(ml)
      })
    })
    return r
  }



  async onChangeModalInput(field, value) {
    var obj = {}
    this.selectStockMoveLine[field] = obj[field] = Number(value)

    console.log(this.selectStockMoveLine)
    console.log(obj)

    var x = await firstValueFrom(this.odooEM.update<StockMoveLine>(this.selectStockMoveLine, obj))
    this.load()
    this.moveLineQtyModal.hide()

  }


  async updateQty() {
    await this.odooEM.update(this.selectStockMoveLine, {
      qty_done: this.selectStockMoveLine.qty_done
    }).toPromise()

    await this.load()
  }






  async openStockQuantModal(stockMove: StockMove) {
    /// stock.quant con product_id che stai cercando quantity > 0
    /// risolviarray package_id dei quants (forse no)
    // for dei quants
    // quant.package_id.name | quant.available_quantity 

    this.selectStockMove = stockMove



    var productId = Number(stockMove.product_id.id)
    this.stockQuantArray = (await firstValueFrom(
      this.odooEM.search<StockQuant>(new StockQuant(),
        [['quantity', '>', 0], ['product_id', '=', productId], ['lot_id', "!=", false], ['package_id', "!=", false]]
      )))
    this.moveLineQtyModal = new Bootstrap.Modal(document.getElementById('modal'))
    this.moveLineQtyModal.show()
  }

  async onChoosed(stockQuant: StockQuant) {
    this.moveLineQtyModal.hide()

    var product = await firstValueFrom(this.odooEM.resolveSingle<ProductProduct>(new ProductProduct(), stockQuant.product_id))
    var quantiyDone = Math.min(this.mrpProduction.product_qty, product.value.qty_available)

    var x = await firstValueFrom(this.odooEM.call(  // openModal(stockMove: StockMove){
      new StockMoveLine(),
      "create",
      null,
      null,
      {

        "company_id": 1,
        "package_id": stockQuant.package_id.id,
        "location_id": 22,
        "qty_done": 0,
        "product_uom_id": stockQuant.product_id.value.uom_po_id.id,
        "location_dest_id": 15,
        "lot_id": stockQuant.lot_id.id,
        'product_id': product.id,
        "move_id": this.selectStockMove.id
      })
    );

    // prendi stockMove e aggiungi id all'rray delle lines
    // if (typeof x == "number") {
    //   var arrayIDS = this.selectStockMove.move_line_ids.ids
    //   arrayIDS.push(x)
    //   var updatedStockMove = await this.odooEM.update(this.selectStockMove, {
    //     'move_line_ids': arrayIDS,
    //     'product_id': product.id
    //   })
    // }

    // update quantity
    var qty_producing = this.mrpProduction.qty_producing + quantiyDone
    await this.odooEM.update(this.mrpProduction, {
      'qty_producing': qty_producing
    })

    this.load()

    // await this.odooEM.resolve(this.selectStockMove.move_line_ids).toPromise()
    // this.showStockLines(this.selectStockMove)

  }


  async deleteMoveLine(o: MrpProduction, ml: StockMoveLine) {
    if (confirm("Vuoi eliminare davvero questa riga ?")) {
      var x = await this.odooEM.delete(new StockMoveLine(), [ml.id]).pipe(first()).toPromise()
      await this.load()
    }
  }

}
