import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';
import { BehaviorSubject, firstValueFrom, Observable, Subject } from 'rxjs';
import { StockMove } from 'src/app/models/stock.move';
import { OdooEntityManager } from 'src/app/shared/services/odoo-entity-manager.service';
import { StockPicking } from 'src/app/models/stock.picking';
import { StockQuantPackage } from 'src/app/models/stock.quant.package';
import { StockLocationRoute } from 'src/app/models/stock.location.route.model';
import { StockRule } from 'src/app/models/stock.rule.model';
import { StockPickingType } from 'src/app/models/stock.picking.type';
import { CONFIG } from 'src/app/CONFIG';
import { StockMoveLine } from 'src/app/models/stock.move.line';
import { Router } from '@angular/router';
import { ScanPackageModal } from 'src/app/components/modals/scan-package/scan-package.modal';
import { MrpProduction } from 'src/app/models/mrp.production.model';

@Component({
  selector: 'app-pelatura',
  templateUrl: './pelatura.page.html'
})
export class PelaturaPage implements OnInit, AfterViewInit {
  loading = false;

  breadcrumb_config: [string, string][]

  arrayStockLocation: StockLocationRoute[] = []
  arrayRules: StockRule[] = []
  arrayStockPickingType: StockPickingType[];
  arrayStockMove: StockMove[] = []
  arrayStockPicking: StockPicking[] = [];

  stockLocationRoute: StockLocationRoute
  @ViewChild('modal') modal: ScanPackageModal;
  productions: MrpProduction[];


  viewDate: string = new Date().toISOString().slice(0, 10);

  constructor(
    public odooEM: OdooEntityManager,
    private router: Router
  ) { }
 
  ngOnInit(): void {
    this.refresh();
  }

  async nuovaProduzione() {
    await firstValueFrom(this.odooEM.create(new MrpProduction(), {
      "product_id": 1750,
      "picking_type_id": CONFIG.picking_type_pelatura
    }))
  }

  getClassForProduction(p:MrpProduction) {
    
    switch (p.state) {
      case "confirmed": return "bg-danger";
      case "progress": return "bg-warning";
      case "to_close": return "bg-warning";
      case "done": return "bg-success";
      default: return ""
    }
    
  }


  getClassForPesa(p:MrpProduction) {

    if (!p.picking_ids) return ""
    if (!p.picking_ids.values) return ""
    if (!p.picking_ids.values.length) return ""

    if (p.picking_ids.values[0].state == "done") return "bg-success"
    if (p.picking_ids.values[0].state == "assigned") return "bg-warning"

    return "bg-danger"
  }


  openOrCreateWeight(pr: MrpProduction) {
    if (!pr.picking_ids.values) return
    var p = pr.picking_ids.values[0]
    this.router.navigate(['pelatura','picking',p.id])
    // this.router.navigate(['/pesatura-bancale'], { queryParams: { production_id: p.id } });
  }


  async refresh() {
    this.loading = true;
    try {
      const x = this.viewDate.replace(/\//g, "-");
      this.productions = await firstValueFrom(
        this.odooEM.search<MrpProduction>(
          new MrpProduction(), 
          [
            ['date_planned_start','>=', x + ' 00:00:00'], 
            ['date_planned_start','<', x + ' 23:59:00']
          ]
        )
      );
      this.productions.sort((a, b) => b.id - a.id);
      await firstValueFrom(
        this.odooEM.resolveArray(new StockPicking(), this.productions, 'picking_ids')
      );
    } finally {
      this.loading = false;
    }
  }

  // async create() {
  //   var x = await this.odooEM.call(new StockQuantPackage(), 'create_lot', null, null, 7).toPromise()
  // }

  _formatDate(date) {
    var d = new Date(date),
        month = '' + (d.getMonth() + 1),
        day = '' + d.getDate(),
        year = d.getFullYear();

    if (month.length < 2) 
        month = '0' + month;
    if (day.length < 2) 
        day = '0' + day;

    return [year, month, day].join('-');
  }


  async ngAfterViewInit(): Promise<void> {

    // this.viewDate$.subscribe(async (x:string) => {
    //   //x = x.replaceAll("/","-")
    //   x = x.replace(/\//g, "-")
    //   this.productions = await firstValueFrom(this.odooEM.search<MrpProduction>(new MrpProduction(), [['date_planned_start','>=', x+ ' 00:00:00'], ['date_planned_start','<', x+' 23:59:00']]))
    //   this.productions.sort((a, b) => b.id - a.id)
    //   await firstValueFrom( this.odooEM.resolveArray(new StockPicking(),this.productions, 'picking_ids'))
    // })

    var stockLocationRoute = await firstValueFrom(
      this.odooEM.search<StockLocationRoute>(new StockLocationRoute(), [["id", "=", CONFIG.reception_route_id]]))
    
    if (stockLocationRoute.length) {
      this.stockLocationRoute = stockLocationRoute[0]

    }
    await firstValueFrom(
      this.odooEM.resolveArray<StockRule,StockLocationRoute>(new StockRule(), stockLocationRoute, 'rule_ids'))
    

    


return


    // if(stockLocationRoute[0].rule_ids.values){
    //   const r:StockRule = stockLocationRoute[0].rule_ids.values[0]; // "BA/Caseificio"
      
    //   this.arrayStockPicking = await firstValueFrom(this.odooEM.search<StockPicking>(new StockPicking(), 
    //     [["picking_type_id", "=", CONFIG.head_picking_type],['state',"!=",'cancel']],
    //   ))
    //   this.arrayStockPicking.sort((a,b) => (a.scheduled_date > b.scheduled_date) ? 1 : ((b.scheduled_date > a.scheduled_date) ? -1 : 0))
    // }

    // this.stockLocationRoute = stockLocationRoute[0]
    // this.arrayStockMove = await firstValueFrom(this.odooEM.search<StockMove>(new StockMove(),[]))
  }

  openScanModal() {
    this.modal.show()
    // this.showModal = true
    // this.modal.
  }

  async onBarcode(e) {
    this.modal.hide()

    var p = await this.odooEM.search<StockQuantPackage>(new StockQuantPackage(), [['name','=', e]]).toPromise()
    if (p && p.length > 0) {
      var c = await this.odooEM.search<StockMoveLine>(new StockMoveLine(), [['result_package_id.name','=',e],['state', '=' ,'assigned']]).toPromise()
      if (c && c.length > 0) {
        this.router.navigate(['pelatura','picking',c[0].picking_id.id], {queryParams: {
          'p':e
        }})
      }
    }
  }
}
