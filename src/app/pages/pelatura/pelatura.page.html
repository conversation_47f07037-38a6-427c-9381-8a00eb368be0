<div class="d-flex flex-column w-100 h-100">

    <app-nav ></app-nav>

    <div class="d-flex flex-column container-fluid">
        
        <div class="d-flex align-items-center justify-content-between flex-wrap gap-4 my-5">
            <ol class="breadcrumb m-0 ">
            <li class="breadcrumb-item">
                <a [routerLink]="['/']">
                    <i class="fa fa-home"></i>
                </a>
            </li>

            <li class="breadcrumb-item">
                    <a [routerLink]="['/pelatura']">Pelatura</a>
                </li>
            </ol> 

            <div class="d-flex flex-wrap align-items-center gap-2">
                <!-- <button class="btn btn-primary" [routerLink]="['purchase_order/new']">
                    <i class="me-2 fa fa-plus"></i>
                    Lotto singolo
                </button>
     -->
                <button class="btn btn-primary" (click)="nuovaProduzione()"><i class="fa fa-plus"></i> Nuova</button>
                <form>
                    <input 
                        name="date" 
                        class="form-control" 
                        [(ngModel)]="viewDate"
                        (change)="refresh()"
                        type="date">
                </form>
            </div>
            <!-- <button class="btn btn-primary " [routerLink]="['/warehouse/daily']">
                <i class="me-2 fa fa-plus"></i>
                Giornaliera
            </button> -->


            
        </div>

<div class="table-responsive">
  <table class="table w-100 ">
    <thead>
      <tr class="text-nowrap" >
        <th class="">Lotto</th>
        <th  class="w-25 text-center text-uppercase"> 
          <a >Pelatura</a>
        </th>
        <th  class="w-25 text-center text-uppercase"> 
          <a >Pesa</a>
        </th>
      </tr>
    </thead>
    <tbody class="">
      <ng-container *ngFor="let pr of productions">
        <tr  > 
          <td class="text-nowrap ps-3 w-100"  class="" scope="row">{{pr.name}}</td>
          <!-- <td>{{p.scheduled_date | date}}</td> -->
           <!--  [ngClass]="getClassByValue(p)" -->
          <td [ngClass]="getClassForProduction(pr)"  [routerLink]="['/pelatura',pr.id]"  >
          </td>
          <td [ngClass]="getClassForPesa(pr)" (click)="openOrCreateWeight(pr)">
            <ng-container *ngIf="pr && pr.picking_ids.values && pr.picking_ids.values.length ">
              {{pr.picking_ids.values[0].state}}</ng-container>
          </td>
        </tr>
      </ng-container>
    </tbody>
  </table>
</div>



        <!-- <app-task-browser class="mt-3" 
            *ngIf="!loading; else loadingTemplate"
            [productions]="productions"
            [stockLocationRoute]="stockLocationRoute"
        > 
        </app-task-browser> -->

        <ng-template #loadingTemplate>
            <div class="d-flex justify-content-center mt-5">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">Caricamento...</span>
                </div>
            </div>
        </ng-template>

        <div *ngIf="!loading && (!productions || productions.length === 0)" class="text-center mt-5">
            <h4 class="text-muted">Nessun risultato trovato per questa data</h4>
        </div>
    </div>
</div>


<div id="scanButton" class="d-flex justify-content-end me-4">
    <button (click)="openScanModal()" class="btn btn-primary">
        <i class="fa fa-scanner-gun fa-3x"></i>
    </button>
</div>

<app-scan-package-modal #modal (onBarcode)="onBarcode($event)"></app-scan-package-modal>
