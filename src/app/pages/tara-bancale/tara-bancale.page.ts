import { Component } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ToastService } from 'src/app/toast/toast.service';
import { firstValueFrom } from 'rxjs';
import { OdooEntityManager } from 'src/app/shared/services/odoo-entity-manager.service';
import { StockQuantPackage } from 'src/app/models/stock.quant.package';
import { PackageWeightTracking } from 'src/app/models/package.weight.tracking';
@Component({
    selector: 'app-tara-bancale',
    templateUrl: './tara-bancale.page.html',
})
export class TaraBancalePage {
    loading: boolean = false;
    barcode: string | null = null;
    selectedValue: string | null = null;
    isWeighing: boolean = false;
    quantity: string | null = null;
    stockQuantPackage: StockQuantPackage | null = null;

    constructor(
        private router: Router,
        private modalService: NgbModal,
        private activatedRoute: ActivatedRoute,
        private odooEM: OdooEntityManager,
        private toastr: ToastService) { }

    getBarcode() {
        return this.activatedRoute.snapshot.queryParamMap.get('barcode');
    }

    async onBarcodeScan(barcodeValue: string) {
        if (barcodeValue.trim()) {
            await this.load(barcodeValue);
            this.router.navigate(['/tara-bancale'], { queryParams: { barcode: barcodeValue } });
        }
    }

    async ngOnInit() {
        this.activatedRoute.queryParamMap.subscribe(async params => {
            const barcode = params.get('barcode');
            if (barcode) {
                this.barcode = barcode;
                await this.load(barcode)
                console.log("Scanned barcode:", barcode);
            }
        });
    }

    async load(barcode: string) {
        this.loading = true;
        try {

            const stockQuantPackages = await firstValueFrom(this.odooEM.search<StockQuantPackage>(new StockQuantPackage(), [
                ['name', '=', barcode],
            ]));

            if(!stockQuantPackages.length) {
                return;
            }

            // resolve weights

            await firstValueFrom(this.odooEM.resolve(stockQuantPackages[0]?.weight_tracking_ids));

            this.stockQuantPackage = stockQuantPackages[0]
            console.log(stockQuantPackages);
        } catch (error) {
            console.error(error);
        } finally {
            this.loading = false;
        }
    }

    hasTara() {
        return this.stockQuantPackage?.weight_tracking_ids?.values?.find(x => x.tracking_type == 'TARA')
    }

    onPesaClick() {
        this.isWeighing = true;
    }

    async onWeigh(value: string | null) {
        console.log(value);
        
        if (value == null) {
            this.isWeighing = false
            return
        }
        //create weight tracking on odoo
        if (this.hasTara())
            await firstValueFrom(this.odooEM.update(this.hasTara()!, {
                weight: value
            }))
        else
            await firstValueFrom(this.odooEM.create<PackageWeightTracking>(new PackageWeightTracking(), {
                package_id: this.stockQuantPackage?.id,
                tracking_type: 'TARA',
                weight: value
            }))

        this.isWeighing = false
        await this.load(this.barcode!)

    }

    async onConfirm() {
        console.log("Value: ", this.selectedValue);
        const tara = Number(this.selectedValue);
        if (isNaN(tara) || tara <= 0) {
            this.toastr.error('Invalid tara weight selected!', 'Errore');
            return;
        }

        try {
            await firstValueFrom(this.odooEM.update(this.stockQuantPackage!, { tara }));
            this.toastr.success('Peso correttamente registrato!', 'Success');
            await this.load(this.barcode!);

            this.barcode = null;
        } catch (error) {
            console.error(error);
        }
    }
}