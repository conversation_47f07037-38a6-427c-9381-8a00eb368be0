import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { firstValueFrom } from 'rxjs';
import { ProductProduct } from 'src/app/models/product.product';
import { OdooEntityManager } from 'src/app/shared/services/odoo-entity-manager.service';
import { StockMove } from 'src/app/models/stock.move';
import { PurchaseOrder } from 'src/app/models/purchase-order.model';
import { PurchaseOrderLine } from 'src/app/models/purchase-order-line.model';
import { StockMoveLine } from 'src/app/models/stock.move.line';
import { StockPicking } from 'src/app/models/stock.picking';
import { StockProductionLot } from 'src/app/models/stock.production-lot';
import { StockQuantPackage } from 'src/app/models/stock.quant.package';
import { CONFIG } from 'src/app/CONFIG';
import { Action } from 'rxjs/internal/scheduler/Action';
import { BassiDailyReplenishment } from 'src/app/models/bassi.daily_production_row';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { MrpProduction } from 'src/app/models/mrp.production.model';
import { ToastService } from 'src/app/toast/toast.service';

class FormPallet {
  qty:number|undefined = 48
  barcode:string|undefined
  poli_1:string
  poli_2:string
}

class FormModel {
  "product": ProductProduct = new ProductProduct()
  "date_planned_start": string
  "pallets": Array<FormPallet> = []
  "poli_1": number
  "poli_2": number
}

@Component({
  selector: 'app-new-purchasee-order-page',
  templateUrl: './new-purchase-order.page.html'
})
export class NewPurchaseOrderPage implements OnInit {
  products: ProductProduct[];
  modelForm: FormModel = new FormModel()
  picking: StockPicking;
  lotName: string = "";
  loading: boolean;
  replenishment: BassiDailyReplenishment;

  constructor(
    public odooEM: OdooEntityManager,
    public router: Router,
    private activatedRoute: ActivatedRoute,
    private http:HttpClient,
    private toast: ToastService
  ) {}

  async ngOnInit(): Promise<void> {

    var params = this.activatedRoute.snapshot.params
    if (params['replenishment_id']) 
      this.replenishment = (await firstValueFrom(this.odooEM.search<BassiDailyReplenishment>(new BassiDailyReplenishment(), [['id', '=', params['replenishment_id']]])))[0]
    
    if (this.replenishment) {
      
      await firstValueFrom(this.odooEM.resolveSingle(new ProductProduct(), this.replenishment.product_id))
      this.modelForm.product = this.replenishment.product_id.value
      console.log("REPLE", this.replenishment)
    }

    this.products = await firstValueFrom(this.odooEM.search<ProductProduct>(new ProductProduct(),[['purchase_ok',"=",true], ['route_ids','in',CONFIG.reception_route_id]]))

    if (this.replenishment)
      this.modelForm.date_planned_start = this._formatDate(this.replenishment.date)
    else 
      this.modelForm.date_planned_start = this._formatDate(new Date())

    // start with 2 of them
    this.modelForm.pallets.push(new FormPallet())
    this.modelForm.pallets.push(new FormPallet())
  }


  



  _formatDate(date) {
    var d = new Date(date),
        month = '' + (d.getMonth() + 1),
        day = '' + d.getDate(),
        year = d.getFullYear();

    if (month.length < 2) 
        month = '0' + month;
    if (day.length < 2) 
        day = '0' + day;

    return [year, month, day].join('-');
  }



  // produzione icona scanner
  
  // una ugual in picking arrivo ( aprire modale )

  onLotChange() {
    this.modelForm.pallets.forEach((p,i) => {
      p.barcode = this.lotName + "-" + String(i).padStart(2,"0")
    });
  }


  async onSubmit() {
    // TODO endpoint odoo
    // validate dati todo
    if(!this.modelForm.product.id) {
      this.toast.error("Compilare tutti i campi!");
      return;
    }
    
    this.loading = true
    
    // calc the quantity to produce
    let qty_to_produce = 0
    this.modelForm.pallets.forEach(p => {
      if (p.qty)
        qty_to_produce = qty_to_produce + Number(p.qty)
    })

    // create the mo
  
    let mo = {
      picking_type_id : CONFIG.picking_type_caseificio,
      product_id: this.modelForm.product.id,
      product_uom_id: 1,
      product_qty: qty_to_produce,
      date_planned_start: this._formatDate(this.modelForm.date_planned_start)
      //bom_id: 3
    }
    var newMO = await firstValueFrom(this.odooEM.create<MrpProduction>(new MrpProduction(), mo))

    if (newMO) {

      await firstValueFrom(this.odooEM.call(new MrpProduction(), "action_bassi_generate_serial",null,null, newMO.id))
      await firstValueFrom(this.odooEM.update(newMO, {
        'qty_producing': qty_to_produce
      }))
  
      // await firstValueFrom(this.odooEM.call(new MrpProduction(), "action_producing_quantity_changed",null, null, newMO.id))
      await firstValueFrom(this.odooEM.call(new MrpProduction(), "button_mark_done",null,null, newMO.id))
      
      console.log("MO", newMO)

      newMO = (await firstValueFrom(this.odooEM.search<MrpProduction>(new MrpProduction(), [['id','=', newMO.id]])))[0]

      var lp = new StockProductionLot(newMO.lot_producing_id.id)
      
      // this.odooEM.update<StockProductionLot>(lp , {
      //   daily_replenishment_id: this.replenishment.id
      // })

      var ps = await firstValueFrom(this.odooEM.search<StockPicking>(new StockPicking(), [['origin', 'ilike', newMO.name]]))

      var p = ps?.find(x => x.picking_type_id.id == CONFIG.head_picking_type)

      if (newMO && p && p.move_line_ids_without_package) {

        let totalPallets = this.modelForm.pallets.length
        // pre create needed packages 
        var packages:StockQuantPackage[] = []
        for (var x = 0; x < totalPallets; x++) {
          var stockQuantPackage = await firstValueFrom(this.odooEM.create<StockQuantPackage>(new StockQuantPackage(),  {
            // package_type_id:3,
            poli_1: this.modelForm.pallets[x].poli_1,
            poli_2: this.modelForm.pallets[x].poli_2,
            // lot_id: newMO.lot_producing_id.id
            // name: this.modelForm.pallets[x].barcode
          } ))
          if (stockQuantPackage)
            packages.push(stockQuantPackage)
        }


        await firstValueFrom(this.odooEM.resolve(p.move_line_ids_without_package))
        let ml:StockMoveLine|null = (p?.move_line_ids_without_package?.values) ? p?.move_line_ids_without_package?.values[0] : null;

        console.log("ML",ml)

       /* console.log("PACLAGES", packages)
        let ml:StockMoveLine|null = (p?.move_line_ids_without_package?.values) ? p?.move_line_ids_without_package?.values[0] : null;
        console.log("ML ", ml,p)
        if (!ml || !p.move_line_ids_without_package.values) {
          return
        }*/

/*
        // update the first line, already createby odoo
        await firstValueFrom(this.odooEM.update<StockMoveLine>(ml,{
          'product_uom_qty': this.modelForm.pallets[0].qty,
          'qty_done': 0,
          'result_package_id': packages[0].id
        }))
        await firstValueFrom(this.odooEM.call(new StockMoveLine(), "action_move_line_changed",null, null, ml.id))
*/

        await firstValueFrom(this.odooEM.call(new StockPicking(),"do_unreserve", null,null, p.id))
        
        // create lines as much line as pallets , skip first


        for (var x = 0; x < totalPallets;x++) {
          console.log("MM", ml)
          var cp = {
            location_dest_id: ml?.location_dest_id.id,
            location_id: ml?.location_id.id,
            lot_id: ml?.lot_id.id,
            move_id: ml?.move_id.id,
            picking_id:ml?.picking_id.id,
            product_id: ml?.product_id.id,
            product_uom_id: 1,
            // product_uom_qty: 0,
            qty_done: this.modelForm.pallets[x].qty,
            result_package_id: packages[x].id,
            company_id:1
          }

          let m = await firstValueFrom(this.odooEM.create<StockMoveLine>(new StockMoveLine(), cp))
          if (m !== null) {
            // await firstValueFrom(this.odooEM.call(new StockMoveLine(), "action_move_line_changed",null, null, m.id))
            p.move_line_ids_without_package.values?.push(m)
          }
        }
        await firstValueFrom(this.odooEM.call(
            new StockPicking(),
            "button_validate",
            null,
            null,
            p.id
          ))  


          // go to first picking - tara
          var pt = ps?.find(x => x.picking_type_id.id == CONFIG.picking_type_tara)

          this.router.navigate(["warehouse", "picking", pt?.id]);
      }

      this.loading = false
    }
    
    // if (this.replenishment)
    //   this.router.navigate([".."]);
    // else
    // this.router.navigate(["/warehouse/"]);
    // --- commit for netlify deploy

  }

  async createPallet() {
    var fp = new FormPallet()
    this.modelForm.pallets.push(fp)
  }

  async deletePallet(p:FormPallet) {
    this.modelForm.pallets =  this.modelForm.pallets.filter( ( obj ) => p != obj );
  }
}