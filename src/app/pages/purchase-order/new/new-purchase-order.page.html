<div class="d-flex flex-column h-100">
  <app-nav></app-nav>

  <div class="d-flex flex-column h-100">
        
    <div class="d-flex align-items-center my-5 mx-4">
      <ol class="breadcrumb m-0 ">
        <li class="breadcrumb-item">
            <a [routerLink]="['/']">
                <i class="fa fa-home"></i>
            </a>
        </li>
        <li class="breadcrumb-item">
                <a [routerLink]="['/warehouse']">Magazzino</a>
        </li>
        <li class="breadcrumb-item">
          <a>Nuovo lotto</a>
        </li>
      </ol> 
    </div>

  <!-- <app-breadcrumb [config]="breadcrumb_config"></app-breadcrumb> -->
<div class="container">
  <form class="mt-3 mx-3">
    <div class="row mt-3">
      <div class="col">
        Data
      </div>
      <div class="col ">
        <input
          type="date"
          [(ngModel)]="modelForm.date_planned_start"
          name="date_planned_start"
          class="form-control"
        />
      </div>
     </div>
     <div class="row mt-3">
      <div class="col ">
        Prodotto
      </div>
      <div class="col ">
        <select
          class="form-select"
          [(ngModel)]="modelForm.product.id"
          name="product"
          required="true"
          aria-label="Default select example"
        >
          <option *ngFor="let p of products" [ngValue]="p.id" [selected]="modelForm.product.name == p.name">{{modelForm.product.name}} {{p.name}}</option>
        </select>
      </div>
    </div>
    <!-- <div class="row mt-3">
      <div class="col ">
        Lotto
      </div>
      <div class="col ">
       <input name="lotname" class="form-control" required="required" (change)="onLotChange()" [(ngModel)]="lotName">
      </div>
    </div>

     -->


    <div class="row mt-2">
      <div class="col">
        <table class="table w-100 ">
          <thead>
            <tr>
              <th colspan="4" class="w-100">Pallets</th>
              <th>
              <a (click)="createPallet()" class="btn btn-primary">
                <i class="fa fa-plus"></i>
              </a>
            </th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let p of modelForm.pallets;let indexOfelement=index;">
              <!-- <td>
                <input
                type="text"
                [(ngModel)]="p.barcode"
                placeholder="Barcode"
                name="barcode_{{indexOfelement}}"
                class="form-control"
              />
              </td> -->
              <td><input
                type="text"
                [(ngModel)]="p.qty"
                placeholder="Quantità"
                name="qty_{{indexOfelement}}"
                class="form-control"
              /></td>
              <td>
                <input name="poli1" class="form-control" placeholder="poli 1" required="required" [(ngModel)]="p.poli_1">
              </td>
              <td>
                <input name="poli2" class="form-control" placeholder="poli 2" required="required"  [(ngModel)]="p.poli_2">
              </td>
              <td>
                <i (click)="deletePallet(p)" class="fa-solid fa-trash-can"></i>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
        
    </div>

    <br>
    <br>
    <br>

    <div class="d-flex mt-3 justify-content-between ">
      <!-- <a class="btn btn-secondary">AZZERA DATI</a> -->
      <button type="button" *ngIf="!loading"  class="btn btn-lg w-100 btn-success text-white" (click)="onSubmit()">
        CREA
      </button>

      <button type="button" disabled *ngIf="loading" class="btn btn-lg w-100 btn-success text-white" >
        <i class="fa fa-spinner"></i>&nbsp;&nbsp;STO CREANDO 
      </button>
    </div>
    
  </form>
</div>
</div>
