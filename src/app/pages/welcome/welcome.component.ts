import { Component, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import Bootstrap from 'bootstrap/dist/js/bootstrap';
import { firstValueFrom } from 'rxjs';
import { ScanPackageModal } from 'src/app/components/modals/scan-package/scan-package.modal';
import { StockQuantPackage } from 'src/app/models/stock.quant.package';
import { OdooEntityManager } from 'src/app/shared/services/odoo-entity-manager.service';

@Component({
  selector: 'app-welcome',
  templateUrl: './welcome.component.html'
})
export class WelcomePage implements OnInit {
  scanModal: any;
  @ViewChild('modal') modal: ScanPackageModal;


  constructor(
    private odooEM:OdooEntityManager,
    private router: Router
  ) { }

  ngOnInit(): void {

  }

  async onBarcode(code) {
    this.modal.hide()
    var ps = await firstValueFrom(this.odooEM.search<StockQuantPackage>(new StockQuantPackage(),[['name', 'ilike', code]] )) 
    if (ps && ps.length) {
      this.router.navigate(["search", ps[0].id])
    }


  }

  scan() {
    this.modal.show()
    // this.scanModal = new Bootstrap.Modal(document.getElementById('scanpackagemodal'))
    // this.scanModal.show()
  }

}
