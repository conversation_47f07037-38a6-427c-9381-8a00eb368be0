/* You can add global styles to this file, and also import other style files */
// @import "../node_modules/bootstrap/scss/bootstrap.scss";

// PremengIcons
@import "primeicons/primeicons.css";


@import url('https://fonts.googleapis.com/css?family=Passion+One');
@import url('https://fonts.googleapis.com/css?family=Montserrat:300,600,700,800');

// $body-bg:  #whit;
// $color: #16375A;


$primary:   #00996d;
$secondary: #B33428;
$success : #00996d;
$warning : #ffff85;
$info :#cac8c8;

$body-color:                #333;

$link-color: #333;
$link-decoration: none;

$container-max-widths: (
    sm: 540px,
    md: 720px,
    lg: 960px,
    xl: 961px,
    xxl: 962px
);

$font-sizes: (
  1: 3rem,  // 30 su 1200
  2: 1.4rem,
  3: 1.25rem,
  4: 1.00rem,
  5: 0.88rem,
  6: 0.65rem
);



// $font-family-base:          "Montserrat", sans-serif;
$line-height-base : 1.2;

// $headings-font-family:        "Passion one",sans-serif;
$headings-font-style:        normal;
$headings-line-height:        1.1;

$h1-font-size:                120px;
$h2-font-size:                80px;
$h3-font-size:                70px;

$font-weight-bold:            700;
$font-weight-bolder:            800;
$font-size-base : 1.25rem;
$font-sizes: (
  1: 1.85rem,  // 30 su 1200
  2: 1.4rem,
  3: 1.25rem,
  4: 1.00rem,
  5: 0.88rem,
  6: 0.65rem
);


@import '../node_modules/bootstrap/scss/bootstrap';

h1 u {
    text-underline-offset: 20px;
}


html,body {
    min-height: 100%;
}

// component
app-warehouse {
    max-width: 100%;
    overflow: hidden;
    min-height: 100vh;
    display: block;
}

.btn-primary {
    color: white;
}

td {
    height: 80px;
    vertical-align: middle;
    border-left: 1px solid white;
}

html, body, app-root, app-welcome {
    width: 100%;
    height: 100%;
}

app-welcome{
    min-height: 90%;
}

app-login {
    display: flex;
    min-height: 100vh;
    justify-content: center;
    align-items: center;
}


#scanButton {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 40px;
    height: 80px;
}