{"name": "bassi-frontend", "version": "0.0.1", "scripts": {"ng": "ng", "start": "ng serve --proxy-config proxy.conf.json --host 0.0.0.0 --port 4210", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^17.3.12", "@angular/common": "^17.3.12", "@angular/compiler": "^17.3.12", "@angular/core": "^17.3.12", "@angular/forms": "^17.3.12", "@angular/platform-browser": "^17.3.12", "@angular/platform-browser-dynamic": "^17.3.12", "@angular/router": "^17.3.12", "@angular/service-worker": "^17.3.12", "@ng-bootstrap/ng-bootstrap": "^15.0.0", "@popperjs/core": "^2.11.5", "add": "^2.0.6", "boostrap": "^2.0.0", "bootstrap": "^5.1.3", "moment": "^2.29.3", "ng": "^0.0.0", "primeicons": "^7.0.0", "primeng": "17", "rxjs": "~7.5.0", "socket.io-client": "^4.8.1", "tslib": "^2.3.0", "zone.js": "~0.14.10"}, "devDependencies": {"@angular-devkit/build-angular": "^17.3.16", "@angular/cli": "^17.3.16", "@angular/compiler-cli": "^17.3.12", "@types/jasmine": "~3.10.0", "@types/node": "^12.11.1", "jasmine-core": "~4.0.0", "karma": "~6.3.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.1.0", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "~1.7.0", "typescript": "~5.4.5"}}