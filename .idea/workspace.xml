<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="90eebb32-b8ca-491e-a61d-78a8ee77268c" name="Changes" comment="fixes the packages" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="2Fl22ykPdIwQgdygw9u7v8Armfp" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;dart.analysis.tool.window.visible&quot;: &quot;false&quot;,
    &quot;last_opened_file_path&quot;: &quot;/Users/<USER>/Development/bassispa/odoo-bassi-frontend&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;yarn&quot;,
    &quot;ts.external.directory.path&quot;: &quot;/Users/<USER>/Applications/WebStorm.app/Contents/plugins/javascript-impl/jsLanguageServicesImpl/external&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager" selected="npm.Angular CLI Server">
    <configuration name="Tests (microsan-frontend)" type="JavaScriptTestRunnerKarma">
      <config-file value="$PROJECT_DIR$/karma.conf.js" />
      <karma-package-dir value="$PROJECT_DIR$/node_modules/@angular/cli" />
      <working-directory value="$PROJECT_DIR$" />
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="Angular Application" type="JavascriptDebugType" uri="http://localhost:4200">
      <method v="2" />
    </configuration>
    <configuration name="Angular CLI Server" type="js.build_tools.npm">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="start" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="90eebb32-b8ca-491e-a61d-78a8ee77268c" name="Changes" comment="" />
      <created>1665052146652</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1665052146652</updated>
      <workItem from="1665052150487" duration="5145000" />
      <workItem from="1674636652902" duration="459000" />
      <workItem from="1675930925315" duration="5730000" />
      <workItem from="1676026951250" duration="6949000" />
      <workItem from="1676455941332" duration="5748000" />
      <workItem from="1676887297888" duration="268000" />
      <workItem from="1676909581319" duration="6228000" />
      <workItem from="1680597164655" duration="1291000" />
      <workItem from="1680849643929" duration="5770000" />
      <workItem from="1681731166432" duration="2000" />
      <workItem from="1681997665823" duration="2450000" />
      <workItem from="1683122770605" duration="511000" />
      <workItem from="1685001622744" duration="272000" />
      <workItem from="1685352386313" duration="18000" />
      <workItem from="1688653524907" duration="1079000" />
      <workItem from="1688981044656" duration="43000" />
      <workItem from="1696344441128" duration="518000" />
      <workItem from="1696502289596" duration="1241000" />
    </task>
    <task id="LOCAL-00001" summary="change api redirect">
      <created>1665396954943</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1665396954943</updated>
    </task>
    <task id="LOCAL-00002" summary="add package metadata">
      <created>1675933964492</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1675933964493</updated>
    </task>
    <task id="LOCAL-00003" summary="fix context">
      <created>1676299546426</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1676299546426</updated>
    </task>
    <task id="LOCAL-00004" summary="sync">
      <created>1676467729632</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1676467729632</updated>
    </task>
    <task id="LOCAL-00005" summary="commit for netlify deploy">
      <created>1677052343589</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1677052343589</updated>
    </task>
    <task id="LOCAL-00006" summary="add button for printer, fix date_planned_start both on create and listing">
      <created>1680886069338</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1680886069338</updated>
    </task>
    <task id="LOCAL-00007" summary="production orders ordering DESC">
      <created>1680887376323</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1680887376324</updated>
    </task>
    <task id="LOCAL-00008" summary="add update date">
      <created>1680888171342</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1680888171342</updated>
    </task>
    <task id="LOCAL-00009" summary="add ES2021.String">
      <created>1680932028058</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1680932028058</updated>
    </task>
    <task id="LOCAL-00010" summary="fix number interpreted as string">
      <created>1681998224997</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1681998224997</updated>
    </task>
    <task id="LOCAL-00011" summary="add revision">
      <created>1681998267537</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1681998267537</updated>
    </task>
    <task id="LOCAL-00012" summary="fixes the packages">
      <created>1681999519114</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1681999519114</updated>
    </task>
    <option name="localTasksCounter" value="13" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.History.Properties">
    <option name="COLUMN_ID_ORDER">
      <list>
        <option value="Default.Root" />
        <option value="Default.Author" />
        <option value="Default.Date" />
        <option value="Default.Subject" />
      </list>
    </option>
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="change api redirect" />
    <MESSAGE value="add package metadata" />
    <MESSAGE value="fix context" />
    <MESSAGE value="sync" />
    <MESSAGE value="commit for netlify deploy" />
    <MESSAGE value="add button for printer, fix date_planned_start both on create and listing" />
    <MESSAGE value="production orders ordering DESC" />
    <MESSAGE value="add update date" />
    <MESSAGE value="add ES2021.String" />
    <MESSAGE value="fix number interpreted as string" />
    <MESSAGE value="add revision" />
    <MESSAGE value="fixes the packages" />
    <option name="LAST_COMMIT_MESSAGE" value="fixes the packages" />
  </component>
</project>