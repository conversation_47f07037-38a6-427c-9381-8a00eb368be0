#import

per pelatura
import  Grogonzola dolce e per perzioni piu' vecchia di 50 giorni dalla data di produzione
mascarpone e' gia su odoo 16



Su bancali
    Etichetta
    Tara 

Pelatura 
proudzione mi aspetto 2 step , produzione con sscelta componenti, scan output 
e poi pesa del output


# Semilavorato torte
in componenti teniamo Peso 
completa produzioni rimando dov'era (magazzino prodotto finito) , lavorano in forma
lotto di produzione di GM e' scrito come 50000+aammgg+produzione




GM



m310
export con download, data e il nome sono prodotto 








DATA CONFEZIONE ? (chiedi dula)


usiamo numero ordine vendita


sposta lotto in fondo e colpsan per lotto e scarto
piu' di un sacco blue


schermate QC 
- visulazzia bancali che tornano da pelatura nn interamente utiulizzati
- creazione ordini di produzioni con pulsante + dal browser



## problem onCreate 
## pesa workingCenter uguale
##  start_room da settare su tutti i pallet?


# MicrosanFrontend

This project was generated with [Angular CLI](https://github.com/angular/angular-cli) version 13.3.1.

## Development server

Run `ng serve` for a dev server. Navigate to `http://localhost:4200/`. The application will automatically reload if you change any of the source files.

## Code scaffolding

Run `ng generate component component-name` to generate a new component. You can also use `ng generate directive|pipe|service|class|guard|interface|enum|module`.

## Build

Run `ng build` to build the project. The build artifacts will be stored in the `dist/` directory.

## Running unit tests

Run `ng test` to execute the unit tests via [Karma](https://karma-runner.github.io).

## Running end-to-end tests

Run `ng e2e` to execute the end-to-end tests via a platform of your choice. To use this command, you need to first add a package that implements end-to-end testing capabilities.

## Further help

To get more help on the Angular CLI use `ng help` or go check out the [Angular CLI Overview and Command Reference](https://angular.io/cli) page.
